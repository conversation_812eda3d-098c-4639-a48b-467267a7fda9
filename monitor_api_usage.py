#!/usr/bin/env python3
"""
OpenAI API Usage Monitor
مانیتور استفاده از API اوپن‌ای‌آی

This script helps monitor your OpenAI API usage and rate limits.
این اسکریپت برای مانیتور کردن استفاده و محدودیت‌های API کمک می‌کند.
"""

import os
import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

API_KEY = os.getenv("OPENAI_API_KEY")
BASE_URL = "https://api.openai.com/v1"

async def check_api_status():
    """Check basic API connectivity and list models."""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/models", headers=headers) as response:
                if response.status == 200:
                    print("✅ API اتصال موفق - API Connection Successful")
                    
                    # Check rate limit headers
                    headers_info = {}
                    for header, value in response.headers.items():
                        if 'limit' in header.lower() or 'remaining' in header.lower():
                            headers_info[header] = value
                    
                    if headers_info:
                        print("\n📊 اطلاعات Rate Limit - Rate Limit Info:")
                        for header, value in headers_info.items():
                            print(f"   {header}: {value}")
                    
                    return True
                elif response.status == 429:
                    print("⚠️  Rate limit خورده‌اید - You've hit the rate limit")
                    retry_after = response.headers.get('retry-after', 'نامشخص')
                    print(f"   تا {retry_after} ثانیه دیگر منتظر بمانید - Wait {retry_after} seconds")
                    return False
                else:
                    print(f"❌ خطا در API - API Error: {response.status}")
                    error_text = await response.text()
                    print(f"   {error_text}")
                    return False
                    
        except aiohttp.ClientError as e:
            print(f"❌ خطای اتصال - Connection Error: {e}")
            return False

async def test_realtime_connection():
    """Test connection to Realtime API endpoint."""
    print("\n🔗 تست اتصال به Realtime API - Testing Realtime API Connection...")
    
    # Note: Realtime API uses WebSocket, so we can't easily test with HTTP
    # But we can check if the model is available
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/models", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    realtime_models = [
                        model for model in data.get('data', [])
                        if 'realtime' in model.get('id', '').lower()
                    ]
                    
                    if realtime_models:
                        print(f"✅ {len(realtime_models)} مدل Realtime موجود - Realtime models available")
                        for model in realtime_models[:3]:  # Show first 3
                            print(f"   - {model['id']}")
                    else:
                        print("⚠️  مدل Realtime یافت نشد - No Realtime models found")
                        print("   ممکن است نیاز به ارتقا account داشته باشید")
                
        except Exception as e:
            print(f"❌ خطا در تست Realtime - Realtime test error: {e}")

def get_recommendations():
    """Provide recommendations based on current setup."""
    print("\n💡 توصیه‌ها - Recommendations:")
    
    vad_threshold = float(os.getenv("VAD_THRESHOLD", "0.5"))
    vad_silence = int(os.getenv("VAD_SILENCE_DURATION_MS", "500"))
    
    if vad_threshold < 0.6:
        print("   🎯 VAD_THRESHOLD را بالاتر ببرید (0.6 یا بیشتر) - Increase VAD_THRESHOLD")
    
    if vad_silence < 800:
        print("   ⏱️  VAD_SILENCE_DURATION_MS را بیشتر کنید (800+ میلی‌ثانیه) - Increase silence duration")
    
    print("   📈 Usage Dashboard را بررسی کنید - Check Usage Dashboard:")
    print("      https://platform.openai.com/usage")
    
    print("   💰 Rate Limits را بررسی کنید - Check Rate Limits:")
    print("      https://platform.openai.com/account/limits")
    
    print("   📚 مستندات Rate Limiting - Rate Limiting Docs:")
    print("      https://platform.openai.com/docs/guides/rate-limits")

async def main():
    """Main monitoring function."""
    print("🚀 شروع مانیتور API - Starting API Monitor")
    print("=" * 50)
    
    if not API_KEY:
        print("❌ OPENAI_API_KEY پیدا نشد - API Key not found")
        print("   لطفاً در فایل .env تنظیم کنید - Please set in .env file")
        return
    
    # Mask API key for display
    masked_key = API_KEY[:10] + "..." + API_KEY[-4:]
    print(f"🔑 API Key: {masked_key}")
    
    # Check basic API status
    api_ok = await check_api_status()
    
    if api_ok:
        # Test Realtime API
        await test_realtime_connection()
    
    # Show current configuration
    print("\n⚙️  تنظیمات فعلی - Current Settings:")
    print(f"   VAD_THRESHOLD: {os.getenv('VAD_THRESHOLD', '0.5')}")
    print(f"   VAD_SILENCE_DURATION_MS: {os.getenv('VAD_SILENCE_DURATION_MS', '500')}")
    print(f"   OPENAI_REALTIME_MODEL: {os.getenv('OPENAI_REALTIME_MODEL', 'gpt-4o-realtime-preview')}")
    print(f"   OPENAI_REALTIME_VOICE: {os.getenv('OPENAI_REALTIME_VOICE', 'alloy')}")
    
    # Provide recommendations
    get_recommendations()

if __name__ == "__main__":
    asyncio.run(main())