"""
Persian-specific function tools for LiveKit Agent
ابزارهای کاربردی برای دستیار صوتی فارسی
"""

import asyncio
import aiohttp
from datetime import datetime, timezone
import jdatetime
from livekit.agents import function_tool
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


@function_tool
async def get_persian_date_time() -> Dict[str, Any]:
    """
    دریافت تاریخ و زمان فعلی به شمسی و میلادی
    
    Returns:
        Dict containing Persian (Shamsi) and Gregorian date/time
    """
    try:
        # Get current time
        now = datetime.now(timezone.utc)
        
        # Convert to Persian date
        persian_date = jdatetime.datetime.fromtimestamp(now.timestamp())
        
        return {
            "persian_date": persian_date.strftime("%Y/%m/%d"),
            "persian_time": persian_date.strftime("%H:%M:%S"),
            "persian_full": f"{persian_date.strftime('%A، %d %B %Y - %H:%M')}",
            "gregorian_date": now.strftime("%Y-%m-%d"),
            "gregorian_time": now.strftime("%H:%M:%S"),
            "timezone": "UTC"
        }
    except Exception as e:
        logger.error(f"Error getting Persian date/time: {e}")
        return {"error": f"خطا در دریافت تاریخ: {str(e)}"}


@function_tool  
async def persian_number_to_text(number: str) -> Dict[str, str]:
    """
    تبدیل عدد به متن فارسی
    
    Args:
        number: The number to convert to Persian text
        
    Returns:
        Dict with number and its Persian text representation
    """
    try:
        # Basic Persian number words (can be expanded)
        ones = ["", "یک", "دو", "سه", "چهار", "پنج", "شش", "هفت", "هشت", "نه"]
        tens = ["", "", "بیست", "سی", "چهل", "پنجاه", "شصت", "هفتاد", "هشتاد", "نود"]
        teens = ["ده", "یازده", "دوازده", "سیزده", "چهارده", "پانزده", "شانزده", "هفده", "هجده", "نوزده"]
        
        num = int(number.strip())
        
        if num == 0:
            return {"number": number, "persian_text": "صفر"}
        
        if 1 <= num <= 9:
            return {"number": number, "persian_text": ones[num]}
        elif 10 <= num <= 19:
            return {"number": number, "persian_text": teens[num - 10]}
        elif 20 <= num <= 99:
            ten_digit = num // 10
            one_digit = num % 10
            result = tens[ten_digit]
            if one_digit > 0:
                result += " و " + ones[one_digit]
            return {"number": number, "persian_text": result}
        else:
            return {"number": number, "persian_text": f"عدد {number} (تبدیل کامل پیاده‌سازی نشده)"}
            
    except ValueError:
        return {"error": f"'{number}' یک عدد معتبر نیست"}
    except Exception as e:
        logger.error(f"Error converting number to Persian: {e}")
        return {"error": f"خطا در تبدیل عدد: {str(e)}"}


@function_tool
async def get_tehran_weather() -> Dict[str, Any]:
    """
    دریافت وضعیت آب و هوای تهران
    
    Returns:
        Dict with Tehran weather information  
    """
    try:
        # This is a mock implementation. In a real scenario, you would call a weather API
        # برای استفاده واقعی، باید از API آب و هوا استفاده کنید
        
        mock_weather = {
            "city": "تهران", 
            "temperature": "18°C",
            "condition": "آفتابی",
            "humidity": "45%",
            "wind_speed": "12 km/h",
            "description": "هوای امروز تهران آفتابی و مطبوع است"
        }
        
        return mock_weather
        
    except Exception as e:
        logger.error(f"Error getting Tehran weather: {e}")
        return {"error": f"خطا در دریافت اطلاعات آب و هوا: {str(e)}"}


@function_tool
async def persian_text_analyzer(text: str) -> Dict[str, Any]:
    """
    تحلیل متن فارسی - شمارش کلمات، حروف و جملات
    
    Args:
        text: Persian text to analyze
        
    Returns:
        Dict with text analysis results
    """
    try:
        if not text.strip():
            return {"error": "متن خالی است"}
        
        # Count sentences (simplified)
        sentences = len([s for s in text.split('.') + text.split('؟') + text.split('!') if s.strip()])
        
        # Count words
        words = len(text.split())
        
        # Count characters (excluding spaces)
        characters = len(text.replace(' ', ''))
        
        # Count total characters (including spaces)  
        total_characters = len(text)
        
        return {
            "original_text": text[:100] + "..." if len(text) > 100 else text,
            "sentences": sentences,
            "words": words, 
            "characters_without_spaces": characters,
            "total_characters": total_characters,
            "analysis": f"این متن شامل {sentences} جمله، {words} کلمه و {characters} حرف است."
        }
        
    except Exception as e:
        logger.error(f"Error analyzing Persian text: {e}")
        return {"error": f"خطا در تحلیل متن: {str(e)}"}


@function_tool
async def calculate_simple_math(expression: str) -> Dict[str, Any]:
    """
    محاسبه عبارات ریاضی ساده
    
    Args:
        expression: Simple math expression (e.g., "2+3", "10*5", "20/4")
        
    Returns:
        Dict with calculation result
    """
    try:
        # Security: Only allow safe mathematical operations
        allowed_chars = set('0123456789+-*/.()')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return {"error": "فقط اعداد و عملگرهای ساده (+, -, *, /, (), .) مجاز هستند"}
        
        # Evaluate the expression safely
        result = eval(expression)
        
        return {
            "expression": expression,
            "result": result,
            "description": f"نتیجه محاسبه {expression} برابر با {result} است"
        }
        
    except ZeroDivisionError:
        return {"error": "تقسیم بر صفر امکان‌پذیر نیست"}
    except Exception as e:
        logger.error(f"Error calculating math expression: {e}")
        return {"error": f"خطا در محاسبه: {str(e)}"}


# Additional helper functions for Persian language processing
def is_persian_char(char: str) -> bool:
    """Check if a character is Persian/Farsi"""
    persian_range = (0x0600, 0x06FF)  # Arabic/Persian Unicode range
    return persian_range[0] <= ord(char) <= persian_range[1]


def has_persian_content(text: str) -> bool:
    """Check if text contains Persian characters"""
    return any(is_persian_char(char) for char in text)