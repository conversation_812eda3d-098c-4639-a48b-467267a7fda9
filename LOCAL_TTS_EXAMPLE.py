"""
مثال استفاده از Local TTS API
مثال کامل از نحوه کارکرد با API محلی شما
"""

import asyncio
import aiohttp
from custom_tts import CustomAPITTS, ChunkedStream


async def test_local_tts_api():
    """تست Local TTS API"""
    
    print("🔥 Testing Local TTS API Integration")
    print("=" * 50)
    
    # تنظیمات API محلی
    api_config = {
        "api_url": "http://localhost:8000/api/generate-sync",
        "api_key": None,  # بدون نیاز به API key
        "voice": "Nova", 
        "voice_affect": "Friendly",
        "voice_instructions": "Voice Affect: Calm and professional."
    }
    
    print(f"📡 API URL: {api_config['api_url']}")
    print(f"🎤 Voice: {api_config['voice']}")
    print(f"😊 Voice Affect: {api_config['voice_affect']}")
    
    # ساختن payload مانند نمونه شما
    test_payload = {
        "query": "سلام من علی رضا مرتضایی هستم . الان ظهر هست ساعت دوازده و نیم هست ما درحال کار هستیم",
        "voice_instructions": "Voice Affect: Calm and professional.",
        "voice_name": "Nova",
        "voice_affect": "Friendly"
    }
    
    print("\n📦 Test Payload:")
    for key, value in test_payload.items():
        print(f"   {key}: {value}")
    
    try:
        # تست مستقیم با aiohttp
        print("\n🧪 Testing direct API call...")
        async with aiohttp.ClientSession() as session:
            async with session.post(
                api_config["api_url"],
                json=test_payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=30.0)
            ) as response:
                print(f"📡 Response Status: {response.status}")
                print(f"📋 Content-Type: {response.content_type}")
                
                if response.status == 200:
                    audio_data = await response.read()
                    print(f"🎵 Received audio: {len(audio_data)} bytes")
                    
                    # بررسی فرمت audio
                    if audio_data[:4] == b'RIFF':
                        print("✅ Audio format: WAV")
                    elif audio_data[:3] == b'ID3' or audio_data[:2] == b'\xff\xfb':
                        print("✅ Audio format: MP3") 
                    else:
                        print("⚠️ Audio format: Unknown")
                        print(f"First 16 bytes: {audio_data[:16]}")
                        
                else:
                    error_text = await response.text()
                    print(f"❌ API Error: {error_text}")
        
        # تست با CustomAPITTS
        print("\n🎯 Testing CustomAPITTS integration...")
        async with CustomAPITTS(
            api_url=api_config["api_url"],
            api_key=api_config["api_key"],
            voice=api_config["voice"],
            voice_affect=api_config["voice_affect"],
            voice_instructions=api_config["voice_instructions"]
        ) as tts:
            
            # ساخت stream
            stream = ChunkedStream(
                tts=tts,
                text="سلام! من یک تست هستم.",
                voice=api_config["voice"]
            )
            
            print("✅ TTS Stream created successfully")
            print("🎊 Local TTS API integration is ready!")
            
    except aiohttp.ClientConnectorError:
        print("❌ Connection Error: Make sure your local TTS API is running on localhost:8000")
        print("💡 Start your API server first, then run this test")
        
    except asyncio.TimeoutError:
        print("❌ Timeout Error: API took too long to respond")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        import traceback
        traceback.print_exc()


def show_curl_examples():
    """نمایش مثال‌های curl برای تست"""
    
    print("\n🌐 cURL Examples for Testing:")
    print("-" * 40)
    
    # مثال ساده
    print("1️⃣ Basic Test:")
    print("""curl -X POST "http://localhost:8000/api/generate-sync" \\
  -H "Content-Type: application/json" \\
  -d '{
    "query": "سلام دنیا",
    "voice_name": "Nova",
    "voice_affect": "Friendly",
    "voice_instructions": "Voice Affect: Calm and professional."
  }' \\
  --output test_basic.wav""")
    
    # مثال پیشرفته
    print("\n2️⃣ Advanced Test (Your Example):")
    print("""curl -X POST "http://localhost:8000/api/generate-sync" \\
  -H "Content-Type: application/json" \\
  -d '{
    "query": "سلام من علی رضا مرتضایی هستم . الان ظهر هست ساعت دوازده و نیم هست ما درحال کار هستیم",
    "voice_instructions": "Voice Affect: Calm and professional.",
    "voice_name": "Nova", 
    "voice_affect": "Friendly"
  }' \\
  --output test_advanced.wav""")
  
    # مثال voice های مختلف
    print("\n3️⃣ Different Voice Affects:")
    affects = ["Friendly", "Professional", "Calm", "Excited"]
    for affect in affects:
        print(f"""# {affect} Voice:
curl -X POST "http://localhost:8000/api/generate-sync" \\
  -H "Content-Type: application/json" \\
  -d '{{"query": "تست صدای {affect}", "voice_name": "Nova", "voice_affect": "{affect}"}}' \\
  --output test_{affect.lower()}.wav
""")


async def main():
    """تست کامل"""
    
    print("🎤 Local TTS API Testing Suite")
    print("=" * 50)
    
    # نمایش مثال‌های curl
    show_curl_examples()
    
    # اجرای تست
    await test_local_tts_api()
    
    print("\n" + "=" * 50)
    print("🎊 Test completed!")
    print("💡 If tests pass, your LiveKit Agent is ready to use with local TTS!")


if __name__ == "__main__":
    asyncio.run(main())