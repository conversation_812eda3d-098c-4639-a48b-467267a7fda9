import asyncio
import os
import time
import random
import logging
from typing import Optional

from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, function_tool
from livekit.plugins import google, groq, silero
from custom_tts import CustomAPITTS
from persian_tools import (
    get_persian_date_time, 
    persian_number_to_text,
    get_tehran_weather,
    persian_text_analyzer,
    calculate_simple_math
)

# Optional: load .env if present
try:
    from dotenv import load_dotenv
    load_dotenv()
except Exception:
    pass

# Configure logging for better error tracking
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@function_tool
async def lookup_weather(location: str) -> dict:
    """Dummy weather lookup example tool."""
    return {"weather": "sunny", "temperature_c": 25, "location": location}


async def exponential_backoff_retry(func, max_retries=3, base_delay=1.0):
    """Retry a function with exponential backoff for rate limiting."""
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if "429" in str(e) or "Too Many Requests" in str(e):
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"Rate limit hit, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"Max retries exceeded for rate limiting: {e}")
                    raise
            else:
                # Non-rate-limit error, don't retry
                raise
    return None


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the LiveKit agent."""
    # Connect to LiveKit if running in dev/start mode
    await ctx.connect()

    # Configure components
    logger.info("Initializing agent components...")
    
    # 1. Voice Activity Detection (VAD) - Silero
    logger.info("Loading Silero VAD...")
    vad = silero.VAD.load()
    
    # 2. Speech-to-Text (STT) - Groq with Persian language
    logger.info("Initializing Groq STT for Persian...")
    stt = groq.STT(
        model="whisper-large-v3-turbo",
        language="fa",  # Persian language
    )
    
    # 3. Language Model - Google Gemini 2.0 Flash  
    logger.info("Initializing Google Gemini LLM...")
    llm = google.LLM(
        model="gemini-2.0-flash",
        temperature=0.8,
    )
    
    # 4. Text-to-Speech - Custom API implementation (Local API)
    logger.info("Initializing custom TTS for local API...")
    # Get TTS API configuration from environment variables
    tts_api_url = os.getenv("TTS_API_URL")
    if not tts_api_url:
        raise ValueError("TTS_API_URL environment variable is required")
    
    tts_api_key = os.getenv("TTS_API_KEY")  # Optional for local API
    tts_voice = os.getenv("TTS_VOICE", "Nova")  # Default to Nova
    tts_voice_affect = os.getenv("TTS_VOICE_AFFECT", "Friendly")  # Voice emotion
    tts_voice_instructions = os.getenv("TTS_VOICE_INSTRUCTIONS", "Voice Affect: Calm and professional.")  # Voice instructions
    
    # Initialize custom TTS with async context manager
    custom_tts = CustomAPITTS(
        api_url=tts_api_url,
        api_key=tts_api_key if tts_api_key else None,
        voice=tts_voice,
        voice_affect=tts_voice_affect,
        voice_instructions=tts_voice_instructions,
        sample_rate=24000,
    )

    # Create the agent with instructions and tools
    agent = Agent(
        instructions=(
            "شما یک دستیار صوتی هوشمند هستید که با استفاده از LiveKit Agents و Gemini ساخته شده‌اید. "
            "پاسخ‌های خود را مختصر و مفید نگه دارید. اگر ابزاری می‌تواند کمک کند، از آن استفاده کنید. "
            "شما ابزارهای مفیدی برای کار با زبان فارسی، تاریخ شمسی، محاسبات، و آب و هوا دارید."
        ),
        tools=[
            lookup_weather,
            get_persian_date_time,
            persian_number_to_text,
            get_tehran_weather,
            persian_text_analyzer,
            calculate_simple_math
        ],
    )

    # Create the AgentSession with all components
    logger.info("Creating AgentSession with all components...")
    session = AgentSession(
        vad=vad,           # Silero VAD for voice activity detection
        stt=stt,           # Groq STT for Persian speech recognition
        llm=llm,           # Google Gemini for language understanding
        tts=custom_tts,    # Custom API for text-to-speech
    )

    # Add error handling for the session
    @session.on("error")
    def on_session_error(error):
        logger.error(f"Session error occurred: {error}")
        if "429" in str(error) or "Too Many Requests" in str(error):
            logger.warning("Rate limiting detected. Consider reducing audio input frequency.")
            # Schedule async sleep in background
            asyncio.create_task(asyncio.sleep(2))

    # Start session with retry mechanism for rate limiting
    async def start_session():
        logger.info("Starting AgentSession...")
        async with custom_tts:  # Use async context manager for TTS
            await session.start(agent=agent, room=ctx.room)
    
    await exponential_backoff_retry(start_session, max_retries=2, base_delay=2.0)
    logger.info("AgentSession started successfully!")

    # Send a proactive greeting in Persian
    async def send_intro():
        await session.generate_reply(
            instructions="سلام! من دستیار صوتی هوشمند شما هستم که با قدرت Gemini و LiveKit کار می‌کنم. چطور می‌تونم بهتون کمک کنم؟"
        )
    
    try:
        await exponential_backoff_retry(send_intro, max_retries=2, base_delay=1.0)
        logger.info("Introduction sent successfully!")
    except Exception as e:
        logger.warning(f"Could not send introduction due to error: {e}")


if __name__ == "__main__":
    # Run locally with: python agent.py console
    # Or connect to LiveKit with: python agent.py dev
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))