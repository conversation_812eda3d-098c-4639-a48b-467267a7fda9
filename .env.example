# LiveKit credentials (required for dev/start modes)
LIVEKIT_URL=wss://yourhost.livekit.cloud
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# Google AI (for Gemini LLM)
GOOGLE_API_KEY=your-google-ai-api-key

# Groq (for STT)
GROQ_API_KEY=your-groq-api-key

# Custom TTS API Configuration (Local API - No Authentication Required)
TTS_API_URL=http://localhost:8000/api/generate-sync
TTS_API_KEY=  # Leave empty for local API (no authentication needed)
TTS_VOICE=Nova
TTS_VOICE_AFFECT=Friendly
TTS_VOICE_INSTRUCTIONS=Voice Affect: Calm and professional.

# Available Voice Options:
# - Nova (default, recommended)
# - Other voices supported by your local API

# Voice Affect Options:
# - Friendly (default, warm and approachable)  
# - Professional (formal and clear)
# - Calm (relaxed and soothing)
# - Excited (energetic and enthusiastic)

# Optional: VAD Configuration
VAD_THRESHOLD=0.6
VAD_SILENCE_DURATION_MS=800