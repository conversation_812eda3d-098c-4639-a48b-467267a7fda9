#!/usr/bin/env python3
"""
نسخه تست agent با OpenAI TTS به جای Custom TTS
برای تست سریع تا زمان راه‌اندازی Custom TTS API
"""

import asyncio
import os
import time
import random
import logging
from typing import Optional

from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, function_tool
from livekit.plugins import google, groq, silero, openai

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except Exception:
    pass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@function_tool
async def lookup_weather(location: str) -> dict:
    """ابزار نمونه برای دریافت آب‌وهوا."""
    return {"weather": "آفتابی", "temperature_c": 25, "location": location}


@function_tool
async def get_time() -> dict:
    """دریافت زمان فعلی."""
    import datetime
    now = datetime.datetime.now()
    return {
        "time": now.strftime("%H:%M:%S"),
        "date": now.strftime("%Y-%m-%d"),
        "persian_date": "۱۴۰۳/۱۰/۱۵"  # نمونه
    }


async def exponential_backoff_retry(func, max_retries=3, base_delay=1.0):
    """Retry با exponential backoff برای مدیریت rate limiting."""
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if "429" in str(e) or "Too Many Requests" in str(e):
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"Rate limit hit, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"Max retries exceeded for rate limiting: {e}")
                    raise
            else:
                raise
    return None


async def entrypoint(ctx: JobContext):
    """Main entrypoint برای LiveKit agent."""
    await ctx.connect()

    logger.info("🚀 Initializing Test Agent with OpenAI TTS...")
    
    # Check required environment variables
    required_vars = ["OPENAI_API_KEY"]
    optional_vars = ["GOOGLE_API_KEY", "GROQ_API_KEY"]
    
    missing_required = [var for var in required_vars if not os.getenv(var)]
    missing_optional = [var for var in optional_vars if not os.getenv(var)]
    
    if missing_required:
        raise ValueError(f"Missing required environment variables: {missing_required}")
    
    if missing_optional:
        logger.warning(f"Missing optional API keys: {missing_optional}")
        logger.warning("Agent will use fallback components")
    
    # 1. Voice Activity Detection (VAD) - Silero
    logger.info("📡 Loading Silero VAD...")
    vad = silero.VAD.load()
    logger.info("✅ Silero VAD loaded successfully")
    
    # 2. Speech-to-Text (STT)
    if os.getenv("GROQ_API_KEY"):
        logger.info("🎤 Initializing Groq STT for Persian...")
        stt = groq.STT(
            model="whisper-large-v3-turbo", 
            language="fa",
        )
        logger.info("✅ Groq STT initialized")
    else:
        logger.info("🎤 Using OpenAI STT (fallback)...")
        stt = openai.STT(model="whisper-1", language="fa")
        logger.info("✅ OpenAI STT initialized")
    
    # 3. Language Model
    if os.getenv("GOOGLE_API_KEY"):
        logger.info("🧠 Initializing Google Gemini LLM...")
        llm = google.LLM(
            model="gemini-2.0-flash",
            temperature=0.8,
        )
        logger.info("✅ Google Gemini LLM initialized")
    else:
        logger.info("🧠 Using OpenAI LLM (fallback)...")
        llm = openai.LLM(
            model="gpt-4o-mini",
            temperature=0.8,
        )
        logger.info("✅ OpenAI LLM initialized")
    
    # 4. Text-to-Speech - OpenAI TTS
    logger.info("🔊 Initializing OpenAI TTS...")
    tts = openai.TTS(
        voice="alloy",  # alloy, echo, fable, onyx, nova, shimmer
        model="tts-1",
    )
    logger.info("✅ OpenAI TTS initialized")

    # Create agent with Persian instructions
    agent = Agent(
        instructions=(
            "شما یک دستیار صوتی هوشمند هستید که به زبان فارسی با کاربران صحبت می‌کنید. "
            "نام شما 'آسا' است و با LiveKit و هوش مصنوعی ساخته شده‌اید. "
            "پاسخ‌های خود را واضح، مفید و دوستانه نگه دارید. "
            "اگر ابزاری می‌تواند در پاسخ کمک کند، از آن استفاده کنید. "
            "همیشه به زبان فارسی پاسخ دهید."
        ),
        tools=[lookup_weather, get_time],
    )

    # Create session
    logger.info("🔗 Creating AgentSession...")
    session = AgentSession(
        vad=vad,
        stt=stt, 
        llm=llm,
        tts=tts,
    )
    logger.info("✅ AgentSession created")

    # Add session error handling
    @session.on("error")
    def on_session_error(error):
        logger.error(f"❌ Session error: {error}")
        if "429" in str(error):
            logger.warning("⚠️ Rate limiting detected - slowing down")
            asyncio.create_task(asyncio.sleep(3))
        elif "quota" in str(error).lower():
            logger.error("💸 API quota exceeded - check your billing")
        elif "invalid" in str(error).lower():
            logger.error("🔑 Invalid API key - check your credentials")

    # Start session with retry mechanism
    async def start_session():
        logger.info("🎯 Starting AgentSession...")
        await session.start(agent=agent, room=ctx.room)
        logger.info("✅ AgentSession started successfully!")
    
    await exponential_backoff_retry(start_session, max_retries=2, base_delay=2.0)

    # Send introduction message
    async def send_intro():
        intro_message = (
            "سلام! من آسا هستم، دستیار صوتی هوشمند شما. "
            "با LiveKit و هوش مصنوعی ساخته شده‌ام و می‌تونم بهتون با "
            "آب‌وهوا، زمان و مسائل مختلف کمک کنم. چطور می‌تونم کمکتون کنم؟"
        )
        await session.generate_reply(instructions=intro_message)
    
    try:
        await exponential_backoff_retry(send_intro, max_retries=2, base_delay=1.0)
        logger.info("🎉 Introduction sent successfully!")
    except Exception as e:
        logger.warning(f"⚠️ Could not send introduction: {e}")
    
    logger.info("🎊 Test Agent is ready and running!")


if __name__ == "__main__":
    print("🔧 LiveKit Test Agent with OpenAI TTS")
    print("=" * 50)
    print("Usage:")
    print("  python test_agent.py console  # Local testing with microphone")
    print("  python test_agent.py dev      # Connect to LiveKit server")
    print("  python test_agent.py start    # Production mode")
    print()
    
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))