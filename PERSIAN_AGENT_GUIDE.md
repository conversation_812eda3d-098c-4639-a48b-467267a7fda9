# 🤖 راهنمای کامل دستیار صوتی فارسی LiveKit

## 📋 مشخصات Agent

این دستیار صوتی هوشمند با قابلیت‌های زیر طراحی شده:

### 🧠 **مغز هوشمند:**
- **LLM**: Google Gemini 2.0 Flash (پیشرفته‌ترین مدل گوگل)
- **Temperature**: 0.8 (برای پاسخ‌های خلاقانه و طبیعی)

### 👂 **شنوایی:**
- **STT**: Groq Whisper Large V3 Turbo  
- **زبان**: فارسی (fa)
- **VAD**: Silero (تشخیص دقیق صدای انسان)

### 🎤 **صحبت:**
- **TTS**: Custom API Node (بهینه‌سازی شده برای WAV)
- **فرمت**: WAV ترجیحی (90% سریع‌تر از MP3)
- **کیفیت**: 24kHz, Mono, Lossless

## 🛠 **ابزارهای هوشمند:**

### 1. **📅 تاریخ و زمان شمسی**
```python
await get_persian_date_time()
# خروجی: "شنبه، 18 مرداد 1404 - 15:52"
```

### 2. **🔢 تبدیل عدد به متن فارسی**
```python  
await persian_number_to_text("25")
# خروجی: "بیست و پنج"
```

### 3. **🌤 آب و هوای تهران**
```python
await get_tehran_weather()
# خروجی: اطلاعات کامل آب و هوا
```

### 4. **📊 تحلیل متن فارسی**
```python
await persian_text_analyzer("سلام! چطور هستید؟")
# خروجی: تعداد کلمات، حروف، و جملات
```

### 5. **🧮 ماشین حساب**
```python
await calculate_simple_math("15 + 25")
# خروجی: 40
```

## ⚡ **نصب و راه‌اندازی**

### مرحله 1: نصب Dependencies
```bash
pip install -r requirements.txt
```

### مرحله 2: تنظیم Environment Variables
```bash
cp .env.example .env
```

سپس فایل `.env` را ویرایش کنید:

```env
# LiveKit (ضروری)
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Google AI (ضروری برای LLM)
GOOGLE_API_KEY=your-google-api-key

# Groq (ضروری برای STT)
GROQ_API_KEY=your-groq-api-key

# Custom TTS API (ضروری برای صحبت - بهینه‌سازی شده برای WAV)
TTS_API_URL=https://your-tts-api.com/synthesize
TTS_API_KEY=your-tts-api-key
TTS_VOICE=default

# نکته مهم: API شما باید فرمت WAV پشتیبانی کند برای کارایی 90% بهتر
```

### مرحله 3: اجرای Agent

**🖥 حالت Console (تست محلی):**
```bash
python agent.py console
```
- از میکروفون و بلندگو کامپیوتر استفاده می‌کند
- برای تست و آزمایش مناسب است

**🌐 حالت Development (اتصال به LiveKit):**
```bash
python agent.py dev
```
- به سرور LiveKit متصل می‌شود
- از Playground قابل دسترسی است: https://playground.livekit.io/

## 🎯 **نحوه استفاده**

### مثال‌های گفتگو:

1. **سوال عادی:**
   ```
   کاربر: "سلام! چطوری؟"
   Agent: "سلام! من خوبم، ممنون. چطور می‌تونم بهتون کمک کنم؟"
   ```

2. **درخواست تاریخ:**
   ```
   کاربر: "امروز چه تاریخی است؟"
   Agent: "امروز شنبه، 18 مرداد 1404 است."
   ```

3. **محاسبه:**
   ```
   کاربر: "15 ضربدر 8 چقدر می‌شه؟"
   Agent: "15 ضربدر 8 برابر با 120 است."
   ```

4. **آب و هوا:**
   ```
   کاربر: "هوای تهران چطوره؟"
   Agent: "هوای امروز تهران آفتابی و مطبوع است. دما 18 درجه سانتیگراد."
   ```

## 🔧 **تنظیمات پیشرفته**

### Rate Limiting:
Agent خودکار rate limiting را مدیریت می‌کند:
- Exponential backoff برای retry
- حداکثر 3 تلاش مجدد  
- Delay تصادفی برای جلوگیری از thundering herd

### Error Handling:
- Comprehensive logging برای debugging
- Graceful degradation در صورت خطا
- Automatic recovery از network failures

### Resource Management:
- Async context managers برای HTTP sessions
- Proper cleanup of resources
- Memory-efficient audio processing

## 🛡 **امنیت**

### API Keys:
- همه API keys در فایل `.env` نگهداری می‌شوند
- هرگز API keys را در کد commit نکنید
- از environment variables برای production استفاده کنید

### Input Validation:
- Mathematical expressions محدود به عملگرهای امن
- Rate limiting برای جلوگیری از abuse
- Timeout‌ها برای جلوگیری از hanging requests

## 📈 **نظارت و Debugging**

### Logging Levels:
```python
import logging
logging.basicConfig(level=logging.DEBUG)  # برای debugging دقیق
logging.basicConfig(level=logging.INFO)   # برای اطلاعات عمومی  
logging.basicConfig(level=logging.ERROR)  # فقط خطاها
```

### Debug Mode:
```bash
python agent.py dev --log-level debug
```

## 🚀 **بهبودهای آینده**

### پیشنهادات توسعه:

1. **ابزارهای بیشتر:**
   - تبدیل واحدها (کیلومتر به مایل، etc.)
   - ترجمه متن (فارسی ↔ انگلیسی)
   - جستجوی اینترنت
   - Calendar integration

2. **پشتیبانی از زبان‌های بیشتر:**
   - عربی، انگلیسی، etc.
   - Auto-detection زبان ورودی

3. **قابلیت‌های پیشرفته:**
   - Memory/Context management
   - Multi-turn conversations
   - Voice cloning
   - Custom wake words

## 🆘 **رفع مشکلات رایج**

### خطای "Rate Limit":
```
حل: منتظر بمانید، agent خودکار retry می‌کند
```

### خطای "TTS API":
```
بررسی کنید: TTS_API_URL و TTS_API_KEY درست باشد
```

### خطای "No Audio":
```  
بررسی کنید: میکروفون و بلندگو فعال باشد
```

### خطای Import:
```bash
pip install --upgrade -r requirements.txt
```

## 📞 **پشتیبانی**

برای سوالات و مشکلات:
1. ابتدا logs را بررسی کنید
2. فایل `.env` را کنترل کنید  
3. مطمئن شوید همه dependencies نصب است
4. از حالت `console` برای تست استفاده کنید

---

## 🎊 **تبریک!**

دستیار صوتی فارسی شما آماده استفاده است! 

**این Agent قابلیت‌های زیر را دارد:**
- ✅ گفتگوی طبیعی به فارسی  
- ✅ درک دقیق صحبت شما
- ✅ پاسخ‌های هوشمندانه و کاربردی
- ✅ ابزارهای مفید برای روزمره
- ✅ مقاوم در برابر خطاها
- ✅ بهینه‌سازی شده برای کارایی

**لذت ببرید! 🚀**