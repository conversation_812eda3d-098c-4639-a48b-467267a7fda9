# راهنمای مدیریت Rate Limiting در LiveKit Agent

## مشکلات Rate Limiting

هنگام استفاده از چندین API مختلف (Google, Groq, Custom TTS)، احتمال برخورد با rate limiting وجود دارد.

## علائم Rate Limiting

```
429 Too Many Requests
quota exceeded
rate limit exceeded
```

## راه‌حل‌های پیاده‌سازی شده

### 1. Exponential Backoff
در `agent.py` سیستم retry با exponential backoff پیاده‌سازی شده:

```python
async def exponential_backoff_retry(func, max_retries=3, base_delay=1.0):
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if "429" in str(e) or "Too Many Requests" in str(e):
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    await asyncio.sleep(delay)
                    continue
```

### 2. Session Error Handling
```python
@session.on("error")
def on_session_error(error):
    if "429" in str(error):
        logger.warning("Rate limiting detected")
        asyncio.create_task(asyncio.sleep(2))
```

## تنظیمات برای کاهش Rate Limiting

### Google Gemini
- **Rate Limit**: 60 requests/minute (Free tier)
- **Solution**: Use temperature=0.8 for balanced responses
- **Monitoring**: Check [Google AI Studio Quota](https://makersuite.google.com/app/apikey)

### Groq
- **Rate Limit**: 30 requests/minute (Free tier)  
- **Solution**: Use `whisper-large-v3-turbo` (fastest model)
- **Monitoring**: Check [Groq Console](https://console.groq.com/settings/limits)

### Custom TTS API
- **Rate Limit**: Depends on your implementation
- **Solution**: 30 second timeout in code
- **Caching**: Consider caching common responses

## Best Practices

### 1. Reduce Request Frequency
```python
# در AgentSession، تنظیمات VAD را بهینه کنید:
vad_config = {
    'threshold': 0.6,          # پایین‌تر = حساسیت کمتر
    'silence_duration': 800,   # بالاتر = کمتر trigger می‌شود  
}
```

### 2. Batch Processing
```python
# For multiple short texts, consider batching:
combined_text = " ".join(short_texts)
if len(combined_text) > 10:  # Minimum length threshold
    await tts.synthesize(combined_text)
```

### 3. Caching Strategy
```python
import hashlib
import json
from pathlib import Path

class TTSCache:
    def __init__(self, cache_dir="tts_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def get_cache_key(self, text: str, voice: str) -> str:
        return hashlib.md5(f"{text}:{voice}".encode()).hexdigest()
    
    async def get_cached_audio(self, text: str, voice: str):
        cache_key = self.get_cache_key(text, voice)
        cache_file = self.cache_dir / f"{cache_key}.mp3"
        
        if cache_file.exists():
            return cache_file.read_bytes()
        return None
    
    async def cache_audio(self, text: str, voice: str, audio_data: bytes):
        cache_key = self.get_cache_key(text, voice)
        cache_file = self.cache_dir / f"{cache_key}.mp3"
        cache_file.write_bytes(audio_data)
```

## Monitoring و Debugging

### 1. Log Analysis
```bash
# اجرا با debug logs
export LIVEKIT_LOG_LEVEL=debug
python agent.py console 2>&1 | grep -E "(429|rate|limit|quota)"
```

### 2. API Usage Monitoring
```python
import time
from collections import defaultdict

class APIUsageMonitor:
    def __init__(self):
        self.requests = defaultdict(list)
    
    def log_request(self, service: str):
        self.requests[service].append(time.time())
        
        # Clean old requests (older than 1 minute)
        cutoff = time.time() - 60
        self.requests[service] = [
            req_time for req_time in self.requests[service] 
            if req_time > cutoff
        ]
    
    def get_requests_per_minute(self, service: str) -> int:
        return len(self.requests[service])
    
    def should_throttle(self, service: str, limit: int) -> bool:
        return self.get_requests_per_minute(service) >= limit

# Usage:
monitor = APIUsageMonitor()

# Before each API call:
if monitor.should_throttle("groq", 25):  # Leave 5 requests buffer
    await asyncio.sleep(10)
monitor.log_request("groq")
```

## Emergency Fallbacks

### 1. TTS Fallback
```python
# In custom_tts.py, add fallback mechanism:
async def synthesize_with_fallback(self, text: str):
    try:
        return await self.synthesize(text)
    except Exception as e:
        if "429" in str(e):
            logger.warning("TTS API rate limited, using fallback")
            # Return silence or cached audio
            return self._generate_silence(len(text) * 0.1)  # Rough duration
```

### 2. LLM Fallback
```python
# Simplified responses during rate limiting:
FALLBACK_RESPONSES = {
    "greeting": "سلام! در حال حاضر پردازش کمی کند است، لطفاً صبور باشید.",
    "error": "متأسفم، مشکلی پیش آمده. لطفاً دوباره تلاش کنید.",
}
```

## Performance Optimization

### 1. Async Optimization
```python
# Process components in parallel where possible:
async def parallel_init():
    vad_task = asyncio.create_task(silero.VAD.load())
    tts_task = asyncio.create_task(custom_tts.__aenter__())
    
    vad = await vad_task
    tts = await tts_task
    return vad, tts
```

### 2. Resource Management
```python
# Clean up resources properly:
try:
    async with custom_tts:
        await session.start(agent=agent, room=ctx.room)
finally:
    # Ensure cleanup
    await asyncio.sleep(1)
```

## Testing Rate Limits

```bash
# Test script برای rate limit testing:
python -c "
import asyncio
import aiohttp

async def test_rate_limit():
    async with aiohttp.ClientSession() as session:
        for i in range(100):  # Send many requests quickly
            try:
                # Test your API endpoints
                print(f'Request {i+1}...')
                await asyncio.sleep(0.1)  # Small delay
            except Exception as e:
                print(f'Error at request {i+1}: {e}')
                break

asyncio.run(test_rate_limit())
"
```