# 🏠 **راهنمای کامل تنظیم Local TTS API**

## 🎯 **تبریک! Agent شما برای API محلی بهینه‌سازی شد**

### ✅ **تغییرات اعمال شده:**

## 🔧 **1. حذف نیاز به Authentication:**
```python
# قبل: API key اجباری
headers["Authorization"] = f"Bearer {api_key}"

# بعد: API key اختیاری
if self._tts._api_key:
    headers["Authorization"] = f"Bearer {self._tts._api_key}"
```

## 📦 **2. Payload مطابق API شما:**
```python
# قبل: Generic payload
{
  "text": "...",
  "format": "wav",
  "voice": "..."
}

# بعد: Local API payload
{
  "query": "سلام من علی رضا مرتضایی هستم ...",
  "voice_name": "<PERSON>",
  "voice_affect": "Friendly", 
  "voice_instructions": "Voice Affect: Calm and professional."
}
```

## ⚙️ **3. Environment Variables جدید:**
```env
TTS_API_URL=http://localhost:8000/api/generate-sync
TTS_API_KEY=  # خالی برای API محلی
TTS_VOICE=Nova
TTS_VOICE_AFFECT=Friendly
TTS_VOICE_INSTRUCTIONS=Voice Affect: Calm and professional.
```

---

## 🚀 **نحوه استفاده:**

### **مرحله 1: تنظیم Environment:**
```bash
# کپی کردن فایل نمونه
cp .env.example .env

# ویرایش .env:
TTS_API_URL=http://localhost:8000/api/generate-sync
TTS_API_KEY=  # خالی بگذارید
TTS_VOICE=Nova
TTS_VOICE_AFFECT=Friendly
```

### **مرحله 2: اطمینان از اجرای TTS Server:**
```bash
# مطمئن شوید سرور TTS روی port 8000 اجراست
curl -X POST "http://localhost:8000/api/generate-sync" \
  -H "Content-Type: application/json" \
  -d '{"query": "تست", "voice_name": "Nova", "voice_affect": "Friendly"}'
```

### **مرحله 3: اجرای LiveKit Agent:**
```bash
# تست محلی
python agent.py console

# اتصال به LiveKit
python agent.py dev
```

---

## 🎵 **مشخصات API شما:**

### **Request:**
```http
POST http://localhost:8000/api/generate-sync
Content-Type: application/json

{
  "query": "متن فارسی برای تبدیل",
  "voice_name": "Nova",
  "voice_affect": "Friendly",
  "voice_instructions": "Voice Affect: Calm and professional."
}
```

### **Response:**
```http
200 OK
Content-Type: audio/wav  (یا audio/mpeg)
Content-Length: 123456

[Binary audio data]
```

---

## 🎛 **تنظیمات Voice:**

### **Voice Names:** (بسته به API شما)
- `Nova` (پیشفرض)
- سایر صداها که API شما پشتیبانی می‌کند

### **Voice Affects:**
- `Friendly` (گرم و صمیمی) 
- `Professional` (رسمی و واضح)
- `Calm` (آرام و تسکین‌دهنده)
- `Excited` (پرانرژی و متحمس)

### **Voice Instructions:**
- `"Voice Affect: Calm and professional."` (پیشفرض)
- `"Speak slowly and clearly"`
- `"Use a warm and welcoming tone"`
- `"Express enthusiasm and energy"`

---

## 🧪 **تست API:**

### **تست ساده:**
```bash
curl -X POST "http://localhost:8000/api/generate-sync" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "سلام دنیا",
    "voice_name": "Nova", 
    "voice_affect": "Friendly"
  }' \
  --output test.wav
```

### **تست پیشرفته (مثال شما):**
```bash
curl -X POST "http://localhost:8000/api/generate-sync" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "سلام من علی رضا مرتضایی هستم . الان ظهر هست ساعت دوازده و نیم هست ما درحال کار هستیم",
    "voice_instructions": "Voice Affect: Calm and professional.",
    "voice_name": "Nova",
    "voice_affect": "Friendly"  
  }' \
  --output test_advanced.wav
```

---

## 🔍 **Debug و عیب‌یابی:**

### **خطای اتصال:**
```
❌ Connection Error: Make sure your local TTS API is running
```
**حل:** مطمئن شوید سرور TTS روی `localhost:8000` اجراست

### **خطای فرمت:**
```
❌ Audio format error
```  
**حل:** API باید audio binary برگرداند (WAV یا MP3)

### **خطای Payload:**
```
❌ API Error 400: Invalid request
```
**حل:** مطمئن شوید فیلدهای `query`, `voice_name`, `voice_affect` ارسال می‌شود

---

## 📊 **مزایای Local API:**

| ویژگی | مزیت |
|--------|------|
| **سرعت** | 🚀 کاهش latency شبکه |
| **امنیت** | 🔒 داده‌ها محلی باقی می‌مانند |
| **کنترل** | 🎛 کنترل کامل روی voice و quality |
| **هزینه** | 💰 بدون هزینه API خارجی |
| **قابلیت اطمینان** | 🛡 بدون وابستگی به اینترنت |

---

## 🎊 **گفتگوی نمونه:**

```
👤 کاربر: "سلام! امروز چطوری؟"

🤖 Agent: 
   1. شنوایی: Groq Whisper (فارسی) ✅
   2. تفکر: Google Gemini 2.0 Flash ✅  
   3. پاسخ: "سلام! من خوبم ممنون. چطور می‌تونم کمکتون کنم؟"
   4. صحبت: Local TTS API (Nova, Friendly) ✅

👤 کاربر: "ساعت چنده؟"

🤖 Agent:
   1. استفاده از persian_tools ✅
   2. پاسخ: "الان ساعت ۱۵:۳۰ است، شنبه ۱۸ مرداد ۱۴۰۴"
   3. صحبت: Local TTS با صدای آرام ✅
```

---

## ⚡ **آماده برای اجرا!**

**Agent شما حالا:**
- ✅ با API محلی شما کار می‌کند
- ✅ نیازی به API key ندارد  
- ✅ از voice affects استفاده می‌کند
- ✅ Payload مطابق مثال شماست
- ✅ قابلیت‌های فارسی کامل دارد

```bash
# شروع کنید:
python agent.py console  # تست محلی
python agent.py dev      # اتصال به LiveKit
```

**لذت ببرید! 🚀**