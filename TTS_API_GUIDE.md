# راهنمای پیاده‌سازی TTS API سفارشی

این راهنما نحوه پیاده‌سازی API سفارشی برای تبدیل متن به گفتار (TTS) را توضیح می‌دهد.

## فرمت درخواست

Agent شما یک POST request به endpoint تعریف شده ارسال می‌کند:

### HTTP Request
```http
POST https://your-api-domain.com/synthesize
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY
```

### Request Body
```json
{
    "text": "متن برای تبدیل به صوت",
    "voice": "default",
    "format": "mp3",
    "sample_rate": 24000
}
```

## فرمت پاسخ

API شما باید فایل صوتی را به صورت binary در response برگرداند:

### HTTP Response
```http
HTTP/1.1 200 OK
Content-Type: audio/mpeg
Content-Length: [size]

[Binary MP3 data]
```

## مثال پیاده‌سازی (Python Flask)

```python
from flask import Flask, request, jsonify, send_file
import tempfile
import os

app = Flask(__name__)

@app.route('/synthesize', methods=['POST'])
def synthesize():
    # دریافت درخواست
    data = request.get_json()
    
    # بررسی API key
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': 'Missing or invalid API key'}), 401
    
    api_key = auth_header.split(' ')[1]
    if api_key != "YOUR_EXPECTED_API_KEY":
        return jsonify({'error': 'Invalid API key'}), 401
    
    # استخراج پارامترها
    text = data.get('text', '')
    voice = data.get('voice', 'default')
    audio_format = data.get('format', 'mp3')
    sample_rate = data.get('sample_rate', 24000)
    
    if not text:
        return jsonify({'error': 'Text is required'}), 400
    
    # تبدیل متن به گفتار (پیاده‌سازی واقعی)
    try:
        audio_data = your_tts_function(
            text=text,
            voice=voice,
            format=audio_format,
            sample_rate=sample_rate
        )
        
        # ایجاد فایل موقت
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f'.{audio_format}')
        temp_file.write(audio_data)
        temp_file.close()
        
        # ارسال فایل
        return send_file(
            temp_file.name,
            mimetype=f'audio/{audio_format}',
            as_attachment=False
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        # پاک‌سازی فایل موقت
        if 'temp_file' in locals():
            try:
                os.unlink(temp_file.name)
            except:
                pass

def your_tts_function(text, voice, format, sample_rate):
    """
    اینجا TTS engine واقعی شما قرار می‌گیرد
    مثلاً Google TTS, Azure TTS, یا engine محلی
    """
    # نمونه کد با gTTS (Google Text-to-Speech)
    from gtts import gTTS
    import io
    
    tts = gTTS(text=text, lang='fa', slow=False)
    audio_buffer = io.BytesIO()
    tts.write_to_fp(audio_buffer)
    audio_buffer.seek(0)
    
    return audio_buffer.read()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
```

## مثال پیاده‌سازی (FastAPI)

```python
from fastapi import FastAPI, HTTPException, Header
from fastapi.responses import Response
from pydantic import BaseModel
import tempfile
import os

app = FastAPI()

class TTSRequest(BaseModel):
    text: str
    voice: str = "default"
    format: str = "mp3"
    sample_rate: int = 24000

@app.post("/synthesize")
async def synthesize(
    request: TTSRequest,
    authorization: str = Header(None)
):
    # بررسی API key
    if not authorization or not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Missing or invalid API key")
    
    api_key = authorization.split(' ')[1]
    if api_key != "YOUR_EXPECTED_API_KEY":
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    if not request.text:
        raise HTTPException(status_code=400, detail="Text is required")
    
    try:
        # تبدیل متن به گفتار
        audio_data = await your_async_tts_function(
            text=request.text,
            voice=request.voice,
            format=request.format,
            sample_rate=request.sample_rate
        )
        
        # برگرداندن فایل صوتی
        return Response(
            content=audio_data,
            media_type=f"audio/{request.format}",
            headers={"Content-Disposition": "attachment; filename=speech.mp3"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def your_async_tts_function(text, voice, format, sample_rate):
    """TTS function آسنکرون شما"""
    # پیاده‌سازی TTS
    pass
```

## تست API

### با curl:
```bash
curl -X POST https://your-api-domain.com/synthesize \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "text": "سلام، این یک تست است",
    "voice": "default",
    "format": "mp3",
    "sample_rate": 24000
  }' \
  --output test_speech.mp3
```

### با Python:
```python
import requests

response = requests.post(
    'https://your-api-domain.com/synthesize',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    json={
        'text': 'سلام، این یک تست است',
        'voice': 'default',
        'format': 'mp3',
        'sample_rate': 24000
    }
)

if response.status_code == 200:
    with open('test_speech.mp3', 'wb') as f:
        f.write(response.content)
    print("✅ فایل صوتی ایجاد شد")
else:
    print(f"❌ خطا: {response.status_code} - {response.text}")
```

## نکات مهم

1. **فرمت صوتی**: agent از فرمت‌های MP3, WAV پشتیبانی می‌کند
2. **Sample Rate**: 24000 Hz توصیه می‌شود برای کیفیت بهتر
3. **Timeout**: درخواست‌ها 30 ثانیه timeout دارند
4. **امنیت**: حتماً authentication مناسب پیاده‌سازی کنید
5. **Error Handling**: پیام‌های خطای واضح برگردانید

## TTS Engines پیشنهادی

- **Google Cloud TTS**: کیفیت بالا، پشتیبانی از فارسی
- **Microsoft Azure Cognitive Services**: صدای طبیعی
- **Amazon Polly**: قیمت مناسب
- **Coqui TTS**: رایگان و منبع باز
- **Festival/Espeak**: محلی و سبک