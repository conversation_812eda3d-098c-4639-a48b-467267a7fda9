import asyncio
import os
import logging
from typing import Optional

from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, function_tool
from livekit.plugins import google


# Optional: load .env if present
try:
    from dotenv import load_dotenv
    load_dotenv()
except Exception:
    pass

# Configure logging for better error tracking
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@function_tool
async def get_current_time() -> str:
    """Get the current time."""
    import datetime
    return f"Current time is: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


@function_tool
async def simple_calculator(expression: str) -> str:
    """Calculate simple math expressions like 2+2, 10*5, etc."""
    try:
        # Basic safety: only allow numbers, operators, and parentheses
        allowed_chars = set('0123456789+-*/().() ')
        if not all(c in allowed_chars for c in expression):
            return "Error: Only numbers and basic operators (+, -, *, /, parentheses) are allowed"
        
        result = eval(expression)
        return f"{expression} = {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


@function_tool
async def get_today_jalali_weekday() -> str:
    """نام روز هفته و تاریخ امروز را به تقویم شمسی (جلالی) برمی‌گرداند."""
    import datetime

    # Gregorian to Jalali conversion (algorithmic, no external deps)
    def gregorian_to_jalali(gy: int, gm: int, gd: int):
        g_d_m = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334]
        if gy > 1600:
            jy = 979
            gy -= 1600
        else:
            jy = 0
            gy -= 621
        if gm > 2:
            gy2 = gy + 1
        else:
            gy2 = gy
        days = 365 * gy + (gy2 + 3) // 4 - (gy2 + 99) // 100 + (gy2 + 399) // 400
        days += gd + g_d_m[gm - 1] - 80
        jy += 33 * (days // 12053)
        days %= 12053
        jy += 4 * (days // 1461)
        days %= 1461
        if days > 365:
            jy += (days - 1) // 365
            days = (days - 1) % 365
        if days < 186:
            jm = 1 + days // 31
            jd = 1 + days % 31
        else:
            jm = 7 + (days - 186) // 30
            jd = 1 + (days - 186) % 30
        return jy + 1, jm, jd

    weekday_names_fa = [
        "دوشنبه",  # Python weekday(): Monday=0
        "سه‌شنبه",
        "چهارشنبه",
        "پنجشنبه",
        "جمعه",
        "شنبه",
        "یکشنبه",
    ]
    month_names_fa = [
        "فروردین",
        "اردیبهشت",
        "خرداد",
        "تیر",
        "مرداد",
        "شهریور",
        "مهر",
        "آبان",
        "آذر",
        "دی",
        "بهمن",
        "اسفند",
    ]

    now = datetime.datetime.now()
    jy, jm, jd = gregorian_to_jalali(now.year, now.month, now.day)
    weekday_fa = weekday_names_fa[now.weekday()]
    month_name = month_names_fa[jm - 1]

    return f"امروز {weekday_fa}، {jd} {month_name} {jy} است."


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the Google Realtime LiveKit agent."""
    # Connect to LiveKit if running in dev/start mode
    await ctx.connect()

    # Configure Google Realtime Model
    logger.info("Initializing Google Realtime Model...")
    
    # Check if Google API key is available
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        logger.error("GOOGLE_API_KEY environment variable is required")
        raise ValueError("GOOGLE_API_KEY environment variable is required")
    
    # Create Google Realtime Model
    realtime_model = google.beta.realtime.RealtimeModel(
        model="gemini-2.0-flash-exp",
        voice="Puck",
        temperature=0.8,
        instructions=(
            "You are a helpful AI assistant powered by Google Gemini Realtime API. "
            "لطفاً همیشه به زبان فارسی پاسخ دهید. اگر نمی‌توانید فارسی صحبت کنید، سعی کنید از عربی استفاده کنید. "
            "شما یک دستیار مفید هوش مصنوعی هستید که می‌توانید با سوالات، محاسبات و ارائه زمان فعلی کمک کنید. "
            "پاسخ‌های شما باید گفتگویی و مفید باشند. "
            "You can help with questions, calculations, and provide the current time. "
            "Keep your responses conversational and helpful in Persian/Farsi language. "
            "You have access to tools for getting time and doing calculations."
        ),
    )

    # Create the agent with tools
    agent = Agent(
        instructions=(
            "You are a helpful AI assistant powered by Google Gemini Realtime API. "
            "وقتی کاربر درباره تاریخ امروز یا اینکه امروز چندشنبه است سوال کرد، باید ابزار `get_today_jalali_weekday` را صدا بزنید و نتیجه را عیناً برگردانید. "
            "برای محاسبات ساده از `simple_calculator` و برای زمان دقیق از `get_current_time` استفاده کنید. "
            "Keep your responses conversational and helpful."
        ),
        tools=[
            get_current_time,
            simple_calculator,
            get_today_jalali_weekday,
        ],
    )

    # Create the AgentSession with Google Realtime Model
    logger.info("Creating AgentSession with Google Realtime Model...")
    session = AgentSession(
        llm=realtime_model,
    )

    # Add error handling for the session
    @session.on("error")
    def on_session_error(error):
        logger.error(f"Session error occurred: {error}")
        print(f"❌ Session Error: {error}")

    # Generic event handler to see all events
    def log_all_events(event_name):
        def handler(*args, **kwargs):
            logger.debug(f"Event '{event_name}' triggered with args: {args}, kwargs: {kwargs}")
            print(f"🔔 Event: {event_name}")
            if args:
                for i, arg in enumerate(args):
                    if hasattr(arg, 'text'):
                        print(f"   📝 Arg {i} text: {arg.text}")
                    elif hasattr(arg, '__dict__'):
                        print(f"   📋 Arg {i}: {arg.__dict__}")
                    else:
                        print(f"   📋 Arg {i}: {arg}")
        return handler

    # Add multiple possible event names for speech recognition
    possible_stt_events = [
        "user_speech_committed",
        "speech_committed", 
        "user_message",
        "speech_recognition_final",
        "speech_final",
        "user_transcription",
        "transcription"
    ]

    for event_name in possible_stt_events:
        session.on(event_name, log_all_events(event_name))

    # Add event handlers for speech recognition logging
    @session.on("user_speech_committed")
    def on_user_speech_committed(msg):
        logger.info(f"🎤 [STT] User said: {msg.text}")
        print(f"🎤 [Speech-to-Text] User: {msg.text}")
        print(f"📱 Console Output: User کی گفت: {msg.text}")  # Persian output

    @session.on("agent_speech_committed") 
    def on_agent_speech_committed(msg):
        logger.info(f"🔊 [TTS] Agent said: {msg.text}")
        print(f"🔊 [Text-to-Speech] Agent: {msg.text}")
        print(f"🤖 Console Output: Agent گفت: {msg.text}")  # Persian output

    @session.on("user_started_speaking")
    def on_user_started_speaking():
        logger.info("🎤 User started speaking...")
        print("🎤 User started speaking... (کاربر شروع به صحبت کرد)")

    @session.on("user_stopped_speaking") 
    def on_user_stopped_speaking():
        logger.info("🎤 User stopped speaking")
        print("🎤 User stopped speaking (کاربر صحبت را متوقف کرد)")
        
    # Additional event handlers for better debugging
    @session.on("agent_started_speaking")
    def on_agent_started_speaking():
        logger.info("🔊 Agent started speaking...")
        print("🔊 Agent started speaking... (ای‌آی شروع به صحبت کرد)")

    @session.on("agent_stopped_speaking")
    def on_agent_stopped_speaking():
        logger.info("🔊 Agent stopped speaking")
        print("🔊 Agent stopped speaking (ای‌آی صحبت را متوقف کرد)")

    # Start session
    logger.info("Starting Google Realtime AgentSession...")
    print("\n" + "="*60)
    print("🚀 Google Realtime Agent starting...")
    print("🎤 Ready to listen! Start speaking in any language.")
    print("🔊 سیستم آماده است! شروع به صحبت کنید.")
    print("📝 All STT (Speech-to-Text) will be logged below:")
    print("="*60 + "\n")
    
    await session.start(agent=agent, room=ctx.room)
    logger.info("Google Realtime AgentSession started successfully!")
    print("✅ Agent session started successfully and listening!")


if __name__ == "__main__":
    # Run locally with: python google_realtime_agent.py console
    # Or connect to LiveKit with: python google_realtime_agent.py dev
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))