# LiveKit Agent with Custom Components

این پروژه یک LiveKit Agent پیشرفته است که از کامپوننت‌های مختلف استفاده می‌کند:

- **Google Gemini 2.0 Flash** برای LLM (مدل زبان)
- **Groq Whisper Large v3 Turbo** برای STT (تبدیل گفتار به متن) با پشتیبانی از زبان فارسی
- **Custom TTS API** برای TTS (تبدیل متن به گفتار)
- **Silero VAD** برای تشخیص فعالیت صوتی

## نصب و راه‌اندازی

### 1. نصب Dependencies

```bash
# Install the required packages
pip install -r requirements.txt

# Download VAD model files (first time only)
python agent.py download-files
```

### 2. تنظیم متغیرهای محیطی

فایل `.env.example` را کپی کنید و نام آن را `.env` بگذارید:

```bash
cp .env.example .env
```

سپس متغیرهای زیر را در فایل `.env` تنظیم کنید:

```bash
# LiveKit credentials
LIVEKIT_URL=wss://yourhost.livekit.cloud
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# Google AI API Key (for Gemini)
GOOGLE_API_KEY=your-google-ai-api-key

# Groq API Key (for STT)
GROQ_API_KEY=your-groq-api-key

# Custom TTS API Configuration
TTS_API_URL=https://your-tts-api-endpoint.com/synthesize
TTS_API_KEY=your-tts-api-key
TTS_VOICE=default
```

### 3. راه‌اندازی Custom TTS API

برای استفاده از TTS سفارشی، API شما باید:

1. یک POST endpoint داشته باشد که JSON دریافت می‌کند
2. فرمت درخواست:
```json
{
    "text": "متن برای تبدیل به صوت",
    "voice": "default",
    "format": "mp3",
    "sample_rate": 24000
}
```

3. فایل MP3 یا داده صوتی خام را در response برگرداند

### 4. API Keys مورد نیاز

#### Google AI API Key:
1. به [Google AI Studio](https://makersuite.google.com/app/apikey) بروید
2. یک API key جدید ایجاد کنید
3. آن را در `GOOGLE_API_KEY` قرار دهید

#### Groq API Key:
1. به [Groq Console](https://console.groq.com/keys) بروید
2. یک API key جدید ایجاد کنید  
3. آن را در `GROQ_API_KEY` قرار دهید

#### LiveKit Credentials:
1. به [LiveKit Cloud](https://cloud.livekit.io/) بروید
2. پروژه خود را ایجاد کنید
3. API Key و Secret را از dashboard کپی کنید

## اجرای Agent

### حالت Console (تست محلی):
```bash
python agent.py console
```

### حالت Development (اتصال به LiveKit):
```bash
python agent.py dev
```

### حالت Production:
```bash
python agent.py start
```

## ساختار پروژه

```
├── agent.py              # فایل اصلی agent
├── custom_tts.py         # پیاده‌سازی TTS سفارشی  
├── requirements.txt      # وابستگی‌های Python
├── .env.example         # نمونه تنظیمات
└── README_NEW_IMPLEMENTATION.md
```

## توضیحات کامپوننت‌ها

### 1. Silero VAD
- تشخیص فعالیت صوتی کاربر
- مدل پیش‌آموزش‌دیده محلی
- عملکرد سریع و دقیق

### 2. Groq STT  
- مدل Whisper Large v3 Turbo
- پشتیبانی از زبان فارسی (`language="fa"`)
- سرعت بالا و دقت مناسب

### 3. Google Gemini LLM
- مدل `gemini-2.0-flash`
- `temperature=0.8` برای پاسخ‌های خلاقانه
- پشتیبانی از زبان فارسی

### 4. Custom TTS
- قابلیت فراخوانی API خارجی
- پشتیبانی از عملیات async
- مدیریت خودکار resource cleanup

## عیب‌یابی

### مشکلات رایج:

1. **خطای VAD Model**: 
```bash
python agent.py download-files
```

2. **خطای API Key**:
- بررسی کنید که همه API keyها در `.env` تنظیم شده باشند
- مطمئن شوید که API keyها معتبر هستند

3. **خطای TTS API**:
- بررسی کنید که `TTS_API_URL` در دسترس است
- لاگ‌های شبکه را برای debug بررسی کنید

4. **مشکلات اتصال LiveKit**:
- `LIVEKIT_URL` را بررسی کنید
- credentials را از dashboard دوباره کپی کنید

### Logs مفید:

```bash
# اجرا با لاگ تفصیلی
export LIVEKIT_LOG_LEVEL=debug
python agent.py console
```

## مثال استفاده

پس از اجرا، agent این قابلیت‌ها را دارد:

1. **تشخیص صحبت**: شناسایی زمان شروع و پایان صحبت کاربر
2. **درک گفتار فارسی**: تبدیل صوت فارسی به متن
3. **پردازش هوشمند**: استفاده از Gemini برای درک و پاسخ
4. **تولید صوت**: تبدیل پاسخ به صوت از طریق API سفارشی
5. **ابزارها**: دسترسی به ابزارهایی مانند پیش‌بینی آب‌وهوا

## Development Notes

### اضافه کردن ابزارهای جدید:

```python
@function_tool
async def my_custom_tool(parameter: str) -> dict:
    """توضیح ابزار جدید."""
    # پیاده‌سازی ابزار
    return {"result": "نتیجه"}

# در entrypoint:
agent = Agent(
    instructions="...",
    tools=[lookup_weather, my_custom_tool],  # اضافه کردن ابزار جدید
)
```

### تنظیم TTS API:

اگر فرمت API شما متفاوت است، فایل `custom_tts.py` را ویرایش کنید:

```python
# در متد _synthesize:
payload = {
    "text": self._text,
    "your_custom_field": "value",
    # سایر پارامترهای API شما
}
```

## مجوز و حمایت

این پروژه برای استفاده شخصی و تجاری آزاد است. برای مشکلات فنی و سوالات، لطفاً issue ایجاد کنید.