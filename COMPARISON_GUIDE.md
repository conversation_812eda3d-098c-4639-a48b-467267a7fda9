# مقایسه کد قدیم و جدید - Persian Agent

## تغییرات اصلی:

### 1. **جداسازی کامپوننت‌ها**

#### کد قدیم (google_realtime_agent.py):
```python
# استفاده از Google Realtime برای همه چیز
realtime_model = google.beta.realtime.RealtimeModel(
    model="gemini-2.0-flash-exp",
    voice="Puck",
    temperature=0.8,
    instructions=(...),
)

session = AgentSession(
    llm=realtime_model,  # همه چیز در یک مدل
)
```

#### کد جدید (google_persian_agent_separated.py):
```python
# جداسازی کامپوننت‌ها
persian_stt = google.STT(
    languages="fa-IR",  # فارسی
    detect_language=False,
    interim_results=True,
    punctuate=True,
    model="latest_long"
)

persian_tts = google.TTS(
    language="fa-IR",  # فارسی
    gender="neutral",
    speaking_rate=1.0,
    pitch=0,
    volume_gain_db=0.0
)

openai_llm = openai.LLM(
    model="gpt-4o-mini",  # استفاده از GPT-4o mini
    temperature=0.8
)

session = AgentSession(
    vad=vad,
    stt=persian_stt,    # Google STT فارسی
    llm=openai_llm,     # OpenAI LLM
    tts=persian_tts,    # Google TTS فارسی
)
```

### 2. **تنظیمات زبان فارسی**

- **STT**: `languages="fa-IR"` (کد زبان فارسی ایران)
- **TTS**: `language="fa-IR"` (کد زبان فارسی ایران)
- **Instructions**: همه دستورالعمل‌ها به فارسی

### 3. **مزایای این روش:**

✅ **انعطاف‌پذیری بیشتر**: می‌توانید هر کامپوننت را جداگانه تنظیم کنید
✅ **کنترل بهتر**: کنترل کامل روی STT، LLM، و TTS
✅ **بهینه‌سازی هزینه**: استفاده از GPT-4o mini به جای Gemini
✅ **پشتیبانی بهتر از فارسی**: تنظیمات مخصوص زبان فارسی
✅ **قابلیت تغییر**: می‌توانید هر کامپوننت را با سرویس دیگری جایگزین کنید

### 4. **متغیرهای محیطی مورد نیاز:**

```bash
export GOOGLE_API_KEY="your-google-api-key"
export OPENAI_API_KEY="your-openai-api-key"
export LIVEKIT_URL="wss://yourhost.livekit.cloud"
export LIVEKIT_API_KEY="your-livekit-api-key"
export LIVEKIT_API_SECRET="your-livekit-api-secret"
```

### 5. **نحوه اجرا:**

```bash
# اجرای محلی (console mode)
python google_persian_agent_separated.py console

# اتصال به LiveKit server
python google_persian_agent_separated.py dev
```

### 6. **ویژگی‌های اضافی:**

- **Voice Activity Detection (VAD)**: استفاده از Silero VAD
- **Event Logging**: لاگ کامل تمام رویدادها
- **Persian Instructions**: دستورالعمل‌های فارسی برای Agent
- **Persian Tools**: ابزارهای فارسی مثل تاریخ جلالی

### 7. **تنظیمات قابل سفارشی‌سازی:**

#### برای STT:
```python
persian_stt = google.STT(
    languages="fa-IR",
    detect_language=False,  # غیرفعال کردن تشخیص زبان
    interim_results=True,   # نتایج موقت
    punctuate=True,         # علائم نگارشی
    model="latest_long"     # مدل دقیق‌تر برای متن‌های طولانی
)
```

#### برای TTS:
```python
persian_tts = google.TTS(
    language="fa-IR",
    gender="neutral",       # می‌توانید "male" یا "female" انتخاب کنید
    speaking_rate=1.0,      # سرعت صحبت (0.25 تا 4.0)
    pitch=0,                # زیر و بمی صدا (-20 تا 20)
    volume_gain_db=0.0      # بلندی صدا (-96 تا 16)
)
```

#### برای LLM:
```python
openai_llm = openai.LLM(
    model="gpt-4o-mini",    # یا "gpt-4o", "gpt-3.5-turbo"
    temperature=0.8         # خلاقیت پاسخ‌ها (0 تا 2)
)
```

## نتیجه‌گیری:

این روش جدید به شما امکان می‌دهد:
- از بهترین STT و TTS گوگل برای زبان فارسی استفاده کنید
- از قدرت GPT-4o mini برای پردازش متن بهره‌مند شوید
- کنترل کامل روی هر بخش داشته باشید
- هزینه‌ها را بهینه کنید
- عملکرد بهتری برای زبان فارسی داشته باشید
