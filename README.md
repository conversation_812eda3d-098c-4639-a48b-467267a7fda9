# LiveKit Agent with OpenAI Realtime (Python)

This repo contains a minimal LiveKit Agent using the OpenAI Realtime API via LiveKit Agents Python SDK.

## Prerequisites
- Python 3.9+
- LiveKit Cloud project (or self-hosted LiveKit)
- OpenAI API key with access to Realtime models

## Install
```bash
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## Configure
Copy `.env.example` to `.env` and set values:
```bash
cp .env.example .env
# Edit .env and fill LIVEKIT_URL / LIVEKIT_API_KEY / LIVEKIT_API_SECRET and OPENAI_API_KEY
```

## Run (console mode)
Runs entirely locally in your terminal with mic/speaker.
```bash
python agent.py console
```

## Run (dev mode)
Connects to LiveKit and exposes the agent to the playground.
```bash
python agent.py dev
```

Then open the Agents Playground: https://playground.livekit.io/ and join with your project.

## Notes
- The agent uses `openai.realtime.RealtimeModel` which consumes audio and produces audio directly. STT/TTS are not needed separately.
- You can tune VAD/turn-detection, temperature, and model/voice via environment variables in `.env`.
- Add your own tools using the `@function_tool` decorator.