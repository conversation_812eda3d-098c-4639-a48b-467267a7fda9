#!/usr/bin/env python3
"""
Test script to verify the LiveKit agent setup and components.
This script will test each component individually before running the full agent.
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_components():
    """Test each component individually."""
    
    # Load environment variables
    load_dotenv()
    
    print("🔧 Testing LiveKit Agent Components")
    print("=" * 50)
    
    # Test 1: Check environment variables
    print("\n1. Testing Environment Variables...")
    required_vars = {
        'LIVEKIT_URL': os.getenv('LIVEKIT_URL'),
        'LIVEKIT_API_KEY': os.getenv('LIVEKIT_API_KEY'),
        'LIVEKIT_API_SECRET': os.getenv('LIVEKIT_API_SECRET'),
        'GOOGLE_API_KEY': os.getenv('GOOGLE_API_KEY'),
        'GROQ_API_KEY': os.getenv('GROQ_API_KEY'),
        'TTS_API_URL': os.getenv('TTS_API_URL'),
    }
    
    missing_vars = []
    for var, value in required_vars.items():
        if value:
            print(f"   ✅ {var}: {'*' * (len(value) - 8) + value[-8:]}")
        else:
            missing_vars.append(var)
            print(f"   ❌ {var}: Not set")
    
    if missing_vars:
        print(f"\n⚠️  Warning: Missing environment variables: {', '.join(missing_vars)}")
        print("   Please check your .env file")
    else:
        print("\n✅ All environment variables are set!")
    
    # Test 2: Import LiveKit components
    print("\n2. Testing LiveKit Components Import...")
    
    try:
        from livekit.plugins import google, groq, silero
        from livekit.agents import Agent, AgentSession
        print("   ✅ LiveKit agents imported successfully")
        print("   ✅ Google plugin imported successfully")
        print("   ✅ Groq plugin imported successfully") 
        print("   ✅ Silero plugin imported successfully")
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    
    # Test 3: Test Custom TTS import
    print("\n3. Testing Custom TTS Import...")
    try:
        from custom_tts import CustomAPITTS
        print("   ✅ Custom TTS imported successfully")
    except ImportError as e:
        print(f"   ❌ Custom TTS import error: {e}")
        return False
    
    # Test 4: Test Silero VAD loading
    print("\n4. Testing Silero VAD Loading...")
    try:
        vad = silero.VAD.load()
        print("   ✅ Silero VAD loaded successfully")
        # Test VAD with dummy audio if available
        print("   ✅ VAD is ready for voice activity detection")
    except Exception as e:
        print(f"   ❌ Silero VAD error: {e}")
        print("   💡 Try running: python agent.py download-files")
    
    # Test 5: Test component initialization (without API calls)
    print("\n5. Testing Component Initialization...")
    
    if os.getenv('GROQ_API_KEY'):
        try:
            stt = groq.STT(
                model="whisper-large-v3-turbo",
                language="fa",
            )
            print("   ✅ Groq STT initialized successfully")
        except Exception as e:
            print(f"   ❌ Groq STT error: {e}")
    else:
        print("   ⏭️ Skipping Groq STT test (no API key)")
    
    if os.getenv('GOOGLE_API_KEY'):
        try:
            llm = google.LLM(
                model="gemini-2.0-flash",
                temperature=0.8,
            )
            print("   ✅ Google Gemini LLM initialized successfully")
        except Exception as e:
            print(f"   ❌ Google LLM error: {e}")
    else:
        print("   ⏭️ Skipping Google LLM test (no API key)")
    
    if os.getenv('TTS_API_URL'):
        try:
            tts = CustomAPITTS(
                api_url=os.getenv('TTS_API_URL'),
                api_key=os.getenv('TTS_API_KEY'),
                voice=os.getenv('TTS_VOICE', 'default'),
            )
            print("   ✅ Custom TTS initialized successfully")
        except Exception as e:
            print(f"   ❌ Custom TTS error: {e}")
    else:
        print("   ⏭️ Skipping Custom TTS test (no API URL)")
    
    # Test 6: Test HTTP session for TTS
    print("\n6. Testing HTTP Session for TTS...")
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            print("   ✅ aiohttp session created successfully")
    except Exception as e:
        print(f"   ❌ HTTP session error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Component testing completed!")
    print("\n📋 Next steps:")
    print("   1. Make sure all environment variables are set in .env")
    print("   2. Test the agent with: python agent.py console")
    print("   3. For LiveKit connection: python agent.py dev")
    
    return True

if __name__ == "__main__":
    asyncio.run(test_components())