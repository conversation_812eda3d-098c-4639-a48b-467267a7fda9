import asyncio
import aiohttp
import logging
from typing import <PERSON><PERSON>, AsyncIterator, Union
from livekit import rtc
from livekit.agents import tts

logger = logging.getLogger(__name__)


class SynthesizedAudio:
    """Wrapper for synthesized audio chunk to match LiveKit pattern."""
    
    def __init__(self, frame: rtc.AudioFrame):
        self.frame = frame


class CustomAPITTS(tts.TTS):
    """Custom TTS implementation that calls external API for text-to-speech conversion."""
    
    def __init__(
        self,
        api_url: str,
        api_key: Optional[str] = None,
        voice: Optional[str] = None,
        voice_affect: Optional[str] = None,
        voice_instructions: Optional[str] = None,
        sample_rate: int = 24000,
        http_session: Optional[aiohttp.ClientSession] = None,
    ):
        super().__init__(
            capabilities=tts.TTSCapabilities(
                streaming=False,  # Set to True if your API supports streaming
            ),
            sample_rate=sample_rate,
            num_channels=1,
        )
        
        self._api_url = api_url
        self._api_key = api_key
        self._voice = voice
        self._voice_affect = voice_affect or "Friendly"
        self._voice_instructions = voice_instructions or "Voice Affect: Calm and professional."
        self._sample_rate = sample_rate
        self._http_session = http_session
        self._own_session = http_session is None
        
    async def __aenter__(self):
        if self._own_session:
            self._http_session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.aclose()
    
    async def aclose(self):
        if self._own_session and self._http_session:
            await self._http_session.close()
            self._http_session = None
    
    def synthesize(
        self,
        text: str,
        *,
        voice: Optional[str] = None,
        **kwargs,
    ) -> "ChunkedStream":
        """
        Synthesize text to speech using external API.
        
        Args:
            text: Text to synthesize
            voice: Voice to use (optional)
            **kwargs: Additional parameters
            
        Returns:
            ChunkedStream: Async iterator of audio chunks
        """
        return ChunkedStream(
            tts=self,
            text=text,
            voice=voice or self._voice,
        )


class ChunkedStream:
    """Stream handler for audio chunks from custom TTS API."""
    
    def __init__(
        self,
        tts: CustomAPITTS,
        text: str,
        voice: Optional[str] = None,
    ):
        self._tts = tts
        self._text = text
        self._voice = voice
        
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        pass
        
    def __aiter__(self) -> AsyncIterator[SynthesizedAudio]:
        return self._synthesize()
    
    async def _synthesize(self) -> AsyncIterator[SynthesizedAudio]:
        """Make API call and yield audio chunks."""
        
        # Initialize HTTP session if not available
        if not self._tts._http_session:
            self._tts._http_session = aiohttp.ClientSession()
            self._tts._own_session = True
        
        # Prepare API request
        headers = {
            "Content-Type": "application/json"
        }
        
        # Add API key only if provided (optional for local APIs)
        if self._tts._api_key:
            headers["Authorization"] = f"Bearer {self._tts._api_key}"
            # Or use different auth header format based on your API:
            # headers["X-API-Key"] = self._tts._api_key
        
        # Request payload - optimized for your local TTS API
        payload = {
            "query": self._text,  # Your API uses 'query' instead of 'text'
            "voice_name": self._voice or "Nova",  # Default to Nova voice
            "voice_affect": self._tts._voice_affect,  # Voice emotion/affect
            "voice_instructions": self._tts._voice_instructions  # Voice instructions
        }
        
        # Note: Local API returns audio directly, format/sample_rate handled automatically
        
        try:
            logger.debug(f"Making TTS API request to {self._tts._api_url} with text: {self._text[:100]}...")
            
            async with self._tts._http_session.post(
                self._tts._api_url,
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30.0)  # Adjust timeout as needed
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"TTS API error {response.status}: {error_text}")
                
                # Read the complete audio data
                audio_data = await response.read()
                logger.debug(f"Received {len(audio_data)} bytes of audio data")
                
                if not audio_data:
                    raise Exception("Empty audio response from TTS API")
                
                # Convert audio data to PCM format
                pcm_data = await self._convert_to_pcm(audio_data, response.content_type)
                
                # Create audio frame
                audio_frame = rtc.AudioFrame(
                    data=pcm_data,
                    sample_rate=self._tts._sample_rate,
                    num_channels=1,
                    samples_per_channel=len(pcm_data) // 2,  # 16-bit audio = 2 bytes per sample
                )
                
                # Yield as a single chunk (for non-streaming API)
                yield SynthesizedAudio(audio_frame)
                
        except asyncio.TimeoutError:
            logger.error("TTS API request timed out")
            raise Exception("TTS API request timed out")
        except aiohttp.ClientError as e:
            logger.error(f"TTS API client error: {e}")
            raise Exception(f"TTS API client error: {e}")
        except Exception as e:
            logger.error(f"TTS API error: {e}")
            raise
    
    async def _convert_to_pcm(self, audio_data: bytes, content_type: str) -> bytes:
        """Convert audio data to PCM format based on content type."""
        
        try:
            # Prioritize WAV format for better performance
            if 'wav' in content_type.lower() or 'pcm' in content_type.lower():
                return await self._extract_pcm_from_wav(audio_data)
            
            # Check for WAV format by file signature (fallback)
            if audio_data[:4] == b'RIFF' and audio_data[8:12] == b'WAVE':
                logger.info("Detected WAV format by file signature")
                return await self._extract_pcm_from_wav(audio_data)
            
            # If it's MP3, we need to decode it
            if 'mp3' in content_type.lower() or 'mpeg' in content_type.lower():
                return await self._decode_mp3_to_pcm(audio_data)
            
            # If content type is generic or unknown, try to detect format
            if audio_data[:3] == b'ID3' or audio_data[:2] == b'\xff\xfb':
                # Looks like MP3
                return await self._decode_mp3_to_pcm(audio_data)
            elif audio_data[:4] == b'RIFF':
                # Looks like WAV
                return audio_data[44:]  # Skip header
            else:
                # Assume it's already PCM
                logger.warning(f"Unknown audio format, content_type: {content_type}")
                return audio_data
                
        except Exception as e:
            logger.error(f"Error converting audio to PCM: {e}")
            # Fallback: return original data
            return audio_data
    
    async def _decode_mp3_to_pcm(self, mp3_data: bytes) -> bytes:
        """Decode MP3 data to PCM using available libraries."""
        
        try:
            # Try using pydub (most reliable)
            import io
            from pydub import AudioSegment
            
            # Load MP3 from bytes
            audio = AudioSegment.from_mp3(io.BytesIO(mp3_data))
            
            # Ensure correct format
            audio = audio.set_frame_rate(self._tts._sample_rate)
            audio = audio.set_channels(1)  # Mono
            audio = audio.set_sample_width(2)  # 16-bit
            
            return audio.raw_data
            
        except ImportError:
            logger.warning("pydub not available, trying alternative MP3 decoding")
            
            try:
                # Try using av (PyAV)
                import av
                import io
                
                container = av.open(io.BytesIO(mp3_data))
                stream = container.streams.audio[0]
                
                pcm_data = b''
                for frame in container.decode(stream):
                    # Resample if needed
                    if frame.sample_rate != self._tts._sample_rate:
                        resampler = av.AudioResampler(
                            format='s16',
                            layout='mono',
                            rate=self._tts._sample_rate
                        )
                        frame = resampler.resample(frame)[0]
                    
                    # Convert to bytes
                    pcm_data += frame.to_ndarray().tobytes()
                
                return pcm_data
                
            except ImportError:
                logger.error("Neither pydub nor PyAV available for MP3 decoding")
                logger.error("Install pydub: pip install pydub")
                logger.error("Or install PyAV: pip install av")
                
                # Last resort: return original data and hope for the best
                logger.warning("Returning MP3 data without conversion - may cause audio issues")
                return mp3_data
                
            except Exception as e:
                logger.error(f"Error decoding MP3 with PyAV: {e}")
                return mp3_data
        
        except Exception as e:
            logger.error(f"Error decoding MP3 with pydub: {e}")
            return mp3_data
    
    async def _extract_pcm_from_wav(self, wav_data: bytes) -> bytes:
        """Extract PCM data from WAV file efficiently."""
        
        try:
            # Validate WAV signature
            if len(wav_data) < 44:
                raise ValueError("WAV file too small (less than 44 bytes)")
            
            if wav_data[:4] != b'RIFF' or wav_data[8:12] != b'WAVE':
                # Maybe it's already raw PCM
                logger.warning("Not a valid WAV file, assuming raw PCM")
                return wav_data
            
            # Parse WAV header efficiently
            # Bytes 16-20: Subchunk1Size (usually 16 for PCM)
            subchunk1_size = int.from_bytes(wav_data[16:20], 'little')
            
            # Bytes 20-22: AudioFormat (1 = PCM)
            audio_format = int.from_bytes(wav_data[20:22], 'little')
            if audio_format != 1:
                logger.warning(f"Non-PCM WAV format detected: {audio_format}")
            
            # Bytes 22-24: NumChannels
            channels = int.from_bytes(wav_data[22:24], 'little')
            
            # Bytes 24-28: SampleRate
            sample_rate = int.from_bytes(wav_data[24:28], 'little')
            
            # Bytes 34-36: BitsPerSample
            bits_per_sample = int.from_bytes(wav_data[34:36], 'little')
            
            logger.debug(f"WAV info - Channels: {channels}, Sample Rate: {sample_rate}Hz, "
                        f"Bits: {bits_per_sample}, Format: {audio_format}")
            
            # Find data chunk (skip extra chunks if any)
            data_offset = 20 + subchunk1_size
            while data_offset < len(wav_data) - 8:
                chunk_id = wav_data[data_offset:data_offset + 4]
                chunk_size = int.from_bytes(wav_data[data_offset + 4:data_offset + 8], 'little')
                
                if chunk_id == b'data':
                    # Found data chunk
                    pcm_data = wav_data[data_offset + 8:data_offset + 8 + chunk_size]
                    logger.debug(f"Extracted PCM data: {len(pcm_data)} bytes")
                    
                    # Check if we need to resample or convert
                    needs_conversion = (
                        sample_rate != self._tts._sample_rate or
                        channels != 1 or
                        bits_per_sample != 16
                    )
                    
                    if needs_conversion:
                        logger.info(f"Converting WAV: {sample_rate}Hz/{channels}ch/{bits_per_sample}bit "
                                   f"-> {self._tts._sample_rate}Hz/1ch/16bit")
                        return await self._convert_wav_format(pcm_data, sample_rate, channels, bits_per_sample)
                    
                    return pcm_data
                
                # Move to next chunk
                data_offset += 8 + chunk_size
            
            # If we get here, no data chunk was found
            raise ValueError("No data chunk found in WAV file")
            
        except Exception as e:
            logger.error(f"Error extracting PCM from WAV: {e}")
            # Fallback: assume standard WAV and skip 44 bytes
            if len(wav_data) > 44:
                logger.warning("Using fallback: skipping first 44 bytes")
                return wav_data[44:]
            return wav_data
    
    async def _convert_wav_format(self, pcm_data: bytes, src_rate: int, src_channels: int, src_bits: int) -> bytes:
        """Convert WAV PCM data to target format if needed."""
        
        try:
            # Use pydub for format conversion if available
            from pydub import AudioSegment
            import io
            
            # Create AudioSegment from raw PCM
            audio = AudioSegment(
                data=pcm_data,
                sample_width=src_bits // 8,
                frame_rate=src_rate,
                channels=src_channels
            )
            
            # Convert to target format
            audio = audio.set_frame_rate(self._tts._sample_rate)
            audio = audio.set_channels(1)  # Mono
            audio = audio.set_sample_width(2)  # 16-bit
            
            logger.debug(f"Successfully converted WAV format using pydub")
            return audio.raw_data
            
        except ImportError:
            logger.warning("pydub not available for WAV conversion, using basic resampling")
            # Basic conversion for simple cases
            if src_channels == 2 and src_bits == 16:
                # Convert stereo to mono (simple average)
                samples = len(pcm_data) // 4  # 2 channels * 2 bytes per sample
                mono_data = bytearray()
                
                for i in range(0, len(pcm_data), 4):
                    if i + 3 < len(pcm_data):
                        left = int.from_bytes(pcm_data[i:i+2], 'little', signed=True)
                        right = int.from_bytes(pcm_data[i+2:i+4], 'little', signed=True)
                        mono_sample = (left + right) // 2
                        mono_data.extend(mono_sample.to_bytes(2, 'little', signed=True))
                
                return bytes(mono_data)
            else:
                logger.warning("Complex format conversion not supported without pydub")
                return pcm_data
                
        except Exception as e:
            logger.error(f"Error in WAV format conversion: {e}")
            return pcm_data
    
    async def collect(self) -> rtc.AudioFrame:
        """Collect all audio chunks into a single AudioFrame."""
        frames = []
        async for chunk in self:
            frames.append(chunk.frame)
        
        if not frames:
            raise Exception("No audio frames collected")
        
        # If only one frame, return it directly
        if len(frames) == 1:
            return frames[0]
        
        # Concatenate multiple frames if needed
        all_data = b"".join(frame.data for frame in frames)
        
        return rtc.AudioFrame(
            data=all_data,
            sample_rate=frames[0].sample_rate,
            num_channels=frames[0].num_channels,
            samples_per_channel=len(all_data) // (frames[0].num_channels * 2),
        )