# 🎤 راهنمای کامل تنظیم Custom TTS API

## 🎯 **درک کلی از نحوه کارکرد**

Custom TTS شما به این صورت کار می‌کند:

```
Text Input → HTTP POST → Your TTS API → Audio File (MP3/WAV) → PCM Conversion → LiveKit Agent
```

## 📡 **مشخصات درخواست API**

### 1. **HTTP Method:**
```
POST
```

### 2. **Headers:**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_API_KEY"
}
```

*نکته: نوع Authentication بسته به API شما متفاوت است:*
- `Authorization: Bearer token`
- `X-API-Key: your-key`  
- `API-Key: your-key`

### 3. **Request Body (JSON):**
```json
{
  "text": "متن مورد نظر برای تبدیل به صوت",
  "voice": "default",
  "format": "mp3",
  "sample_rate": 24000
}
```

### 4. **Response:**
- **Status Code:** `200` (موفقیت)
- **Content-Type:** `audio/mpeg` یا `audio/wav`
- **Body:** Raw binary audio data

---

## 🔧 **تنظیم Environment Variables**

در فایل `.env` خود:

```env
# URL endpoint آپی TTS شما
TTS_API_URL=https://your-tts-api.com/synthesize

# کلید API (اختیاری اگر API شما نیاز دارد)
TTS_API_KEY=your-api-key-here

# نوع صدا (اختیاری)
TTS_VOICE=persian_female
```

---

## 🛠 **سازگار کردن با API های مختلف**

### **حالت 1: OpenAI Style API**

اگر API شما مثل OpenAI کار می‌کند:

```python
# در فایل custom_tts.py خط 107 را تغییر دهید:
headers["Authorization"] = f"Bearer {self._tts._api_key}"

# در خط 114-125 payload را تغییر دهید:
payload = {
    "model": "tts-1",
    "input": self._text,
    "voice": self._voice or "alloy",
    "response_format": "mp3"
}
```

### **حالت 2: Google Cloud TTS Style**

```python
# Headers:
headers["Authorization"] = f"Bearer {gcloud_token}"

# Payload:
payload = {
    "input": {"text": self._text},
    "voice": {
        "languageCode": "fa-IR",
        "name": self._voice or "fa-IR-Standard-A"
    },
    "audioConfig": {
        "audioEncoding": "MP3",
        "sampleRateHertz": self._tts._sample_rate
    }
}
```

### **حالت 3: Custom Simple API**

```python
# Headers:
headers["X-API-Key"] = self._tts._api_key

# Payload:
payload = {
    "text": self._text,
    "voice_id": self._voice,
    "output_format": "mp3",
    "quality": "high"
}
```

---

## 🧪 **تست API شما**

برای تست manual API خود از curl استفاده کنید:

```bash
# تست basic
curl -X POST "https://your-api.com/tts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_KEY" \
  -d '{"text": "سلام", "voice": "default", "format": "mp3"}' \
  --output test.mp3

# بررسی فایل خروجی
file test.mp3
```

---

## ⚙️ **تنظیمات پیشرفته**

### **1. تغییر Authentication Method:**

در `custom_tts.py` خط 106-109:

```python
# برای Bearer Token:
if self._tts._api_key:
    headers["Authorization"] = f"Bearer {self._tts._api_key}"

# برای API Key در header:
if self._tts._api_key:
    headers["X-API-Key"] = self._tts._api_key

# برای Basic Auth:
import base64
if self._tts._api_key:
    encoded = base64.b64encode(f"user:{self._tts._api_key}".encode()).decode()
    headers["Authorization"] = f"Basic {encoded}"
```

### **2. تغییر Payload Structure:**

در خط 113-125:

```python
# Payload ساده:
payload = {
    "text": self._text,
    "voice": self._voice,
    "format": "mp3"
}

# Payload پیچیده:
payload = {
    "input": {
        "text": self._text,
        "language": "fa"
    },
    "voice_settings": {
        "voice_id": self._voice,
        "stability": 0.75,
        "similarity_boost": 0.8
    },
    "output_config": {
        "format": "mp3",
        "sample_rate": self._tts._sample_rate,
        "bitrate": 128
    }
}
```

### **3. پردازش Response متفاوت:**

اگر API شما JSON response برمی‌گرداند:

```python
# بجای خط 142: audio_data = await response.read()
response_json = await response.json()

if "audio_url" in response_json:
    # اگر URL صوت برگردانده شود
    audio_url = response_json["audio_url"]
    async with self._tts._http_session.get(audio_url) as audio_response:
        audio_data = await audio_response.read()

elif "audio_base64" in response_json:
    # اگر base64 برگردانده شود
    import base64
    audio_data = base64.b64decode(response_json["audio_base64"])

else:
    # اگر binary مستقیم برگردانده شود
    audio_data = await response.read()
```

---

## 🔍 **Debug و عیب‌یابی**

### **1. فعال کردن Debug Logging:**

```python
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("custom_tts")
```

### **2. بررسی Request/Response:**

```python
# اضافه کردن به _synthesize method:
print(f"🚀 Request URL: {self._tts._api_url}")
print(f"📦 Request Headers: {headers}")
print(f"📋 Request Payload: {payload}")

# بعد از دریافت response:
print(f"📡 Response Status: {response.status}")
print(f"📊 Response Headers: {dict(response.headers)}")
print(f"🎵 Audio Data Size: {len(audio_data)} bytes")
```

### **3. ذخیره فایل برای بررسی:**

```python
# اضافه کردن بعد از خط 142:
# ذخیره فایل خام برای debug
debug_filename = f"debug_audio_{int(time.time())}.mp3"
with open(debug_filename, "wb") as f:
    f.write(audio_data)
print(f"🎵 Debug audio saved: {debug_filename}")
```

---

## 🎛 **نمونه API های محبوب**

### **1. ElevenLabs:**
```bash
curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/VOICE_ID" \
  -H "Accept: audio/mpeg" \
  -H "xi-api-key: YOUR_API_KEY" \
  -d '{"text": "Hello", "voice_settings": {"stability": 0.75}}'
```

### **2. Azure Cognitive Services:**
```bash
curl -X POST "https://region.tts.speech.microsoft.com/cognitiveservices/v1" \
  -H "Ocp-Apim-Subscription-Key: YOUR_KEY" \
  -H "Content-Type: application/ssml+xml" \
  -d '<speak>Hello</speak>'
```

### **3. Amazon Polly:**
```bash
aws polly synthesize-speech \
  --output-format mp3 \
  --voice-id Joanna \
  --text "Hello" \
  output.mp3
```

---

## 🚨 **مشکلات متداول و راه‌حل**

### **❌ خطا: "TTS API error 401"**
**حل:** API key اشتباه یا منقضی
```bash
# تست API key:
curl -H "Authorization: Bearer YOUR_KEY" https://your-api.com/test
```

### **❌ خطا: "Empty audio response"**
**حل:** بررسی payload format
```python
# اضافه کردن debug:
print(f"Response content-type: {response.content_type}")
print(f"Response size: {response.content_length}")
```

### **❌ خطا: "Audio conversion failed"**
**حل:** نصب dependencies
```bash
pip install pydub
# یا
pip install av
```

### **❌ خطا: "Request timeout"**
**حل:** افزایش timeout
```python
timeout=aiohttp.ClientTimeout(total=60.0)  # به جای 30
```

---

## ✅ **نکات مهم**

1. **همیشه از HTTPS استفاده کنید**
2. **API key را امن نگه دارید** (در .env)
3. **Timeout مناسب تنظیم کنید** (30-60 ثانیه)
4. **Error handling کامل داشته باشید**
5. **Rate limiting را رعایت کنید**
6. **Audio quality را بررسی کنید** (24kHz توصیه می‌شود)

---

## 🎊 **آماده‌سازی برای Production**

برای استفاده واقعی:

1. **Load Balancing:** چندین API endpoint
2. **Caching:** صوت‌های تکراری را ذخیره کنید
3. **Monitoring:** Success rate و latency
4. **Fallback:** API جایگزین در صورت خرابی
5. **Compression:** استفاده از فرمت‌های بهینه

---

**Agent شما حالا آماده استفاده با هر API دلخواه شماست! 🚀**