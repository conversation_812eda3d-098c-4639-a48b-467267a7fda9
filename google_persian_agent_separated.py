import asyncio
import os
import logging
from typing import Optional

from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, function_tool
from livekit.plugins import google, openai, silero

# Optional: load .env if present
try:
    from dotenv import load_dotenv
    load_dotenv()
except Exception:
    pass

# Configure logging for better error tracking
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@function_tool
async def get_current_time() -> str:
    """Get the current time."""
    import datetime
    return f"Current time is: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


@function_tool
async def simple_calculator(expression: str) -> str:
    """Calculate simple math expressions like 2+2, 10*5, etc."""
    try:
        # Basic safety: only allow numbers, operators, and parentheses
        allowed_chars = set('0123456789+-*/().() ')
        if not all(c in allowed_chars for c in expression):
            return "Error: Only numbers and basic operators (+, -, *, /, parentheses) are allowed"
        
        result = eval(expression)
        return f"{expression} = {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


@function_tool
async def get_today_jalali_weekday() -> str:
    """نام روز هفته و تاریخ امروز را به تقویم شمسی (جلالی) برمی‌گرداند."""
    import datetime

    # Gregorian to Jalali conversion (algorithmic, no external deps)
    def gregorian_to_jalali(gy: int, gm: int, gd: int):
        g_d_m = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334]
        if gy > 1600:
            jy = 979
            gy -= 1600
        else:
            jy = 0
            gy -= 621
        if gm > 2:
            gy2 = gy + 1
        else:
            gy2 = gy
        days = 365 * gy + (gy2 + 3) // 4 - (gy2 + 99) // 100 + (gy2 + 399) // 400
        days += gd + g_d_m[gm - 1] - 80
        jy += 33 * (days // 12053)
        days %= 12053
        jy += 4 * (days // 1461)
        days %= 1461
        if days > 365:
            jy += (days - 1) // 365
            days = (days - 1) % 365
        if days < 186:
            jm = 1 + days // 31
            jd = 1 + days % 31
        else:
            jm = 7 + (days - 186) // 30
            jd = 1 + (days - 186) % 30
        return jy + 1, jm, jd

    weekday_names_fa = [
        "دوشنبه",  # Python weekday(): Monday=0
        "سه‌شنبه",
        "چهارشنبه",
        "پنجشنبه",
        "جمعه",
        "شنبه",
        "یکشنبه",
    ]
    month_names_fa = [
        "فروردین",
        "اردیبهشت",
        "خرداد",
        "تیر",
        "مرداد",
        "شهریور",
        "مهر",
        "آبان",
        "آذر",
        "دی",
        "بهمن",
        "اسفند",
    ]

    now = datetime.datetime.now()
    jy, jm, jd = gregorian_to_jalali(now.year, now.month, now.day)
    weekday_fa = weekday_names_fa[now.weekday()]
    month_name = month_names_fa[jm - 1]

    return f"امروز {weekday_fa}، {jd} {month_name} {jy} است."


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the Persian LiveKit agent with separated components."""
    # Connect to LiveKit if running in dev/start mode
    await ctx.connect()

    logger.info("Initializing Persian Agent with separated STT, LLM, and TTS...")
    
    # Check if required API keys are available
    google_api_key = os.getenv("GOOGLE_API_KEY")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not google_api_key:
        logger.error("GOOGLE_API_KEY environment variable is required for STT and TTS")
        raise ValueError("GOOGLE_API_KEY environment variable is required")
    
    if not openai_api_key:
        logger.error("OPENAI_API_KEY environment variable is required for LLM")
        raise ValueError("OPENAI_API_KEY environment variable is required")

    # Create Google STT for Persian language
    logger.info("Creating Google STT for Persian (fa-IR)...")
    persian_stt = google.STT(
        languages="fa-IR",  # Persian (Iran)
        detect_language=False,  # We know it's Persian
        interim_results=True,
        punctuate=True,
        model="latest_long"  # Use the latest long model for better accuracy
    )

    # Create Google TTS for Persian language
    logger.info("Creating Google TTS for Persian (fa-IR)...")
    persian_tts = google.TTS(
        language="fa-IR",  # Persian (Iran)
        gender="neutral",  # You can change to "male" or "female" if preferred
        speaking_rate=1.0,
        pitch=0,
        volume_gain_db=0.0
    )

    # Create OpenAI LLM (GPT-4o mini)
    logger.info("Creating OpenAI LLM (GPT-4o mini)...")
    openai_llm = openai.LLM(
        model="gpt-4o-mini",
        temperature=0.8
    )

    # Create VAD (Voice Activity Detection)
    vad = silero.VAD.load()

    # Create the agent with Persian instructions
    agent = Agent(
        instructions=(
            "شما یک دستیار مفید هوش مصنوعی هستید که به زبان فارسی پاسخ می‌دهید. "
            "وقتی کاربر درباره تاریخ امروز یا اینکه امروز چندشنبه است سوال کرد، باید ابزار `get_today_jalali_weekday` را صدا بزنید و نتیجه را عیناً برگردانید. "
            "برای محاسبات ساده از `simple_calculator` و برای زمان دقیق از `get_current_time` استفاده کنید. "
            "همیشه به زبان فارسی پاسخ دهید و پاسخ‌های شما باید گفتگویی و مفید باشند."
        ),
        tools=[
            get_current_time,
            simple_calculator,
            get_today_jalali_weekday,
        ],
    )

    # Create the AgentSession with separated components
    logger.info("Creating AgentSession with separated STT, LLM, and TTS...")
    session = AgentSession(
        vad=vad,                    # Voice Activity Detection
        stt=persian_stt,           # Google STT for Persian
        llm=openai_llm,            # OpenAI GPT-4o mini for text processing
        tts=persian_tts,           # Google TTS for Persian
    )

    # Add error handling for the session
    @session.on("error")
    def on_session_error(error):
        logger.error(f"Session error occurred: {error}")
        print(f"❌ Session Error: {error}")

    # Add event handlers for speech recognition logging
    @session.on("user_speech_committed")
    def on_user_speech_committed(msg):
        logger.info(f"🎤 [STT] User said: {msg.text}")
        print(f"🎤 [Speech-to-Text] User: {msg.text}")
        print(f"📱 Console Output: کاربر گفت: {msg.text}")

    @session.on("agent_speech_committed") 
    def on_agent_speech_committed(msg):
        logger.info(f"🔊 [TTS] Agent said: {msg.text}")
        print(f"🔊 [Text-to-Speech] Agent: {msg.text}")
        print(f"🤖 Console Output: ای‌آی گفت: {msg.text}")

    @session.on("user_started_speaking")
    def on_user_started_speaking():
        logger.info("🎤 User started speaking...")
        print("🎤 User started speaking... (کاربر شروع به صحبت کرد)")

    @session.on("user_stopped_speaking") 
    def on_user_stopped_speaking():
        logger.info("🎤 User stopped speaking")
        print("🎤 User stopped speaking (کاربر صحبت را متوقف کرد)")
        
    @session.on("agent_started_speaking")
    def on_agent_started_speaking():
        logger.info("🔊 Agent started speaking...")
        print("🔊 Agent started speaking... (ای‌آی شروع به صحبت کرد)")

    @session.on("agent_stopped_speaking")
    def on_agent_stopped_speaking():
        logger.info("🔊 Agent stopped speaking")
        print("🔊 Agent stopped speaking (ای‌آی صحبت را متوقف کرد)")

    # Start session
    logger.info("Starting Persian AgentSession with separated components...")
    print("\n" + "="*60)
    print("🚀 Persian Agent starting with separated components...")
    print("🎤 STT: Google Speech-to-Text (Persian)")
    print("🧠 LLM: OpenAI GPT-4o mini")
    print("🔊 TTS: Google Text-to-Speech (Persian)")
    print("🎤 Ready to listen! Start speaking in Persian.")
    print("🔊 سیستم آماده است! شروع به صحبت کنید.")
    print("📝 All STT (Speech-to-Text) will be logged below:")
    print("="*60 + "\n")
    
    await session.start(agent=agent, room=ctx.room)
    logger.info("Persian AgentSession started successfully with separated components!")
    print("✅ Agent session started successfully and listening!")


if __name__ == "__main__":
    # Run locally with: python google_persian_agent_separated.py console
    # Or connect to LiveKit with: python google_persian_agent_separated.py dev
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
