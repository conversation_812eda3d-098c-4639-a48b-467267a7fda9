# راهنمای سریع راه‌اندازی LiveKit Agent

## گام ۱: دریافت API Keys

### Google AI API Key
1. به [Google AI Studio](https://makersuite.google.com/app/apikey) بروید
2. روی "Create API Key" کلیک کنید
3. API key را کپی کنید و در `.env` قرار دهید:
```bash
GOOGLE_API_KEY=your-actual-google-api-key
```

### Groq API Key  
1. به [Groq Console](https://console.groq.com/keys) بروید
2. حساب کاربری ایجاد کنید (رایگان)
3. API key جدید بسازید
4. در `.env` قرار دهید:
```bash
GROQ_API_KEY=your-actual-groq-api-key
```

### Custom TTS API
فعلاً TTS سفارشی شما آماده نیست، پس از OpenAI TTS استفاده می‌کنیم که قبلاً تنظیم شده.

## گام ۲: نصب Dependencies اضافی

```bash
cd /home/<USER>/Desktop/main/projects/livekit_agent
pip install pydub
```

## گام ۳: تست Agent

### تست اولیه بدون API calls:
```bash
python test_setup.py
```

### تست Agent در Console Mode:
```bash
python agent.py console
```

### تست با LiveKit Server:
```bash
python agent.py dev
```

## گام ۴: نمونه نسخه تست

اگر می‌خواهید سریع تست کنید، از `test_agent.py` استفاده کنید که از OpenAI TTS استفاده می‌کند.

## عیب‌یابی رایج

### خطای "No module named 'google'":
```bash
pip install livekit-agents[google]
```

### خطای "No module named 'groq'":
```bash
pip install livekit-agents[groq]
```

### خطای "VAD model not found":
```bash
python agent.py download-files
```

### خطای Rate Limiting:
- در فایل `RATE_LIMITING_GUIDE.md` راه‌حل‌ها موجود است
- کاهش فعالیت یا افزایش delay بین درخواست‌ها

## مانیتورینگ API Usage

```bash
# نصب monitoring script
python monitor_api_usage.py
```

## مراحل بعدی

1. ✅ API keyها را تنظیم کنید
2. ✅ Agent را در console mode تست کنید  
3. ✅ Custom TTS API خود را پیاده‌سازی کنید
4. ✅ Agent را با TTS سفارشی تست کنید
5. ✅ در محیط production قرار دهید

## فایل‌های مهم

- `agent.py` - Agent اصلی با Custom TTS
- `test_agent.py` - نسخه تست با OpenAI TTS
- `custom_tts.py` - پیاده‌سازی TTS سفارشی
- `TTS_API_GUIDE.md` - راهنمای پیاده‌سازی TTS API
- `RATE_LIMITING_GUIDE.md` - مدیریت rate limiting